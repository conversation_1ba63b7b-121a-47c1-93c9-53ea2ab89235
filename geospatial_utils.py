"""
Advanced Geospatial Utilities for Wasfaty Analytics
Provides H3 indexing, spatial clustering, and coverage analysis functions
"""

import pandas as pd
import numpy as np
import h3
from typing import List, Dict, Tuple, Optional
import folium
from folium import plugins
import plotly.express as px
import plotly.graph_objects as go
from sklearn.cluster import DBSCAN
from scipy.spatial.distance import cdist
import warnings
warnings.filterwarnings('ignore')

def add_h3_indices(df: pd.DataFrame, lat_col: str = 'latitude', lon_col: str = 'longitude', 
                   resolutions: List[int] = [7, 8, 9]) -> pd.DataFrame:
    """
    Add H3 indices at multiple resolutions for hierarchical spatial analysis
    
    Args:
        df: DataFrame with latitude and longitude columns
        lat_col: Name of latitude column
        lon_col: Name of longitude column
        resolutions: List of H3 resolutions to compute
    
    Returns:
        DataFrame with additional H3 index columns
    """
    df_copy = df.copy()
    
    for resolution in resolutions:
        h3_col = f'h3_res_{resolution}'
        df_copy[h3_col] = df_copy.apply(
            lambda row: h3.latlng_to_cell(row[lat_col], row[lon_col], resolution)
            if pd.notna(row[lat_col]) and pd.notna(row[lon_col]) else None,
            axis=1
        )
    
    return df_copy

def calculate_h3_demand_density(df: pd.DataFrame, h3_col: str = 'h3_res_8') -> pd.DataFrame:
    """
    Calculate demand density metrics for H3 hexagons
    
    Args:
        df: DataFrame with H3 indices
        h3_col: Column name containing H3 indices
    
    Returns:
        DataFrame with H3 hexagon demand metrics
    """
    h3_stats = df.groupby(h3_col).agg({
        'order_id': 'count',
        'delivery_hour_start': ['mean', 'std'],
        'delivery_day_of_week': lambda x: x.mode().iloc[0] if len(x.mode()) > 0 else 'Unknown',
        'branch_license': 'nunique',
        'is_pickup': 'sum'
    }).round(2)
    
    h3_stats.columns = ['order_count', 'avg_delivery_hour', 'delivery_hour_std', 
                       'peak_day', 'branch_count', 'pickup_count']
    
    # Add hexagon center coordinates
    h3_stats['center_lat'] = h3_stats.index.map(lambda x: h3.cell_to_latlng(x)[0])
    h3_stats['center_lon'] = h3_stats.index.map(lambda x: h3.cell_to_latlng(x)[1])
    
    # Calculate delivery ratio
    h3_stats['delivery_ratio'] = (h3_stats['order_count'] - h3_stats['pickup_count']) / h3_stats['order_count']
    
    return h3_stats.reset_index()

def identify_coverage_gaps(demand_df: pd.DataFrame, branch_df: pd.DataFrame, 
                          max_distance_km: float = 5.0) -> pd.DataFrame:
    """
    Identify areas with high demand but poor branch coverage
    
    Args:
        demand_df: DataFrame with demand locations (lat, lon, demand_score)
        branch_df: DataFrame with branch locations (lat, lon, branch_id)
        max_distance_km: Maximum acceptable distance to nearest branch
    
    Returns:
        DataFrame with coverage gap analysis
    """
    from geopy.distance import geodesic
    
    gaps = []
    
    for _, demand_point in demand_df.iterrows():
        demand_coord = (demand_point['center_lat'], demand_point['center_lon'])
        
        # Calculate distance to nearest branch
        min_distance = float('inf')
        nearest_branch = None
        
        for _, branch in branch_df.iterrows():
            branch_coord = (branch['latitude'], branch['longitude'])
            distance = geodesic(demand_coord, branch_coord).kilometers
            
            if distance < min_distance:
                min_distance = distance
                nearest_branch = branch['branch_license']
        
        # Identify coverage gaps
        if min_distance > max_distance_km:
            gaps.append({
                'h3_index': demand_point['h3_res_8'],
                'center_lat': demand_point['center_lat'],
                'center_lon': demand_point['center_lon'],
                'order_count': demand_point['order_count'],
                'distance_to_nearest_branch': min_distance,
                'nearest_branch': nearest_branch,
                'coverage_gap_score': demand_point['order_count'] * (min_distance / max_distance_km)
            })
    
    return pd.DataFrame(gaps)

def create_temporal_heatmap_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    Create data for temporal heatmaps (hour vs day of week)
    """
    # Create hour-day matrix
    temporal_data = df.groupby(['delivery_hour_start', 'delivery_day_of_week']).size().reset_index(name='order_count')
    
    # Pivot for heatmap
    heatmap_data = temporal_data.pivot(index='delivery_hour_start', 
                                      columns='delivery_day_of_week', 
                                      values='order_count').fillna(0)
    
    # Reorder days
    day_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
    heatmap_data = heatmap_data.reindex(columns=[day for day in day_order if day in heatmap_data.columns])
    
    return heatmap_data

def calculate_branch_performance_metrics(df: pd.DataFrame) -> pd.DataFrame:
    """
    Calculate comprehensive branch performance metrics
    """
    branch_metrics = df.groupby('branch_license').agg({
        'order_id': 'count',
        'area': 'nunique',
        'delivery_hour_start': ['mean', 'std'],
        'is_pickup': ['sum', 'mean'],
        'latitude': 'first',
        'longitude': 'first'
    }).round(2)
    
    branch_metrics.columns = ['total_orders', 'areas_served', 'avg_delivery_hour', 
                             'delivery_hour_std', 'pickup_orders', 'pickup_ratio',
                             'latitude', 'longitude']
    
    # Calculate efficiency metrics
    branch_metrics['orders_per_area'] = branch_metrics['total_orders'] / branch_metrics['areas_served']
    branch_metrics['delivery_orders'] = branch_metrics['total_orders'] - branch_metrics['pickup_orders']
    
    return branch_metrics.reset_index()

def create_supply_demand_map(df: pd.DataFrame, h3_demand: pd.DataFrame, 
                           branch_metrics: pd.DataFrame) -> folium.Map:
    """
    Create interactive map showing supply (branches) vs demand (H3 hexagons)
    """
    # Calculate map center
    center_lat = df['latitude'].mean()
    center_lon = df['longitude'].mean()
    
    # Create base map
    m = folium.Map(
        location=[center_lat, center_lon],
        zoom_start=10,
        tiles='OpenStreetMap'
    )
    
    # Add demand hexagons
    demand_layer = folium.FeatureGroup(name='Demand Density (H3)')
    
    for _, hex_data in h3_demand.iterrows():
        # Get hexagon boundary
        hex_boundary = h3.cell_to_boundary(hex_data['h3_res_8'])
        hex_coords = [[lat, lon] for lat, lon in hex_boundary]
        
        # Color based on demand intensity
        max_orders = h3_demand['order_count'].max()
        intensity = hex_data['order_count'] / max_orders
        color = f'rgba(255, {int(255 * (1 - intensity))}, 0, 0.7)'
        
        folium.Polygon(
            locations=hex_coords,
            color='red',
            weight=1,
            fillColor=color,
            fillOpacity=0.6,
            popup=folium.Popup(f"""
                <b>H3 Hexagon</b><br>
                Orders: {hex_data['order_count']}<br>
                Peak Day: {hex_data['peak_day']}<br>
                Avg Hour: {hex_data['avg_delivery_hour']:.1f}<br>
                Branches: {hex_data['branch_count']}
            """, max_width=200)
        ).add_to(demand_layer)
    
    # Add branch supply points
    supply_layer = folium.FeatureGroup(name='Branch Supply')
    
    for _, branch in branch_metrics.iterrows():
        # Size marker based on order volume
        max_branch_orders = branch_metrics['total_orders'].max()
        marker_size = 5 + (branch['total_orders'] / max_branch_orders) * 15
        
        folium.CircleMarker(
            location=[branch['latitude'], branch['longitude']],
            radius=marker_size,
            color='blue',
            fillColor='lightblue',
            fillOpacity=0.8,
            popup=folium.Popup(f"""
                <b>{branch['branch_license']}</b><br>
                Total Orders: {branch['total_orders']}<br>
                Areas Served: {branch['areas_served']}<br>
                Pickup Ratio: {branch['pickup_ratio']:.2%}<br>
                Orders/Area: {branch['orders_per_area']:.1f}
            """, max_width=200)
        ).add_to(supply_layer)
    
    # Add layers to map
    demand_layer.add_to(m)
    supply_layer.add_to(m)
    
    # Add layer control
    folium.LayerControl().add_to(m)
    
    return m

def generate_operational_insights(df: pd.DataFrame, h3_demand: pd.DataFrame, 
                                branch_metrics: pd.DataFrame, coverage_gaps: pd.DataFrame) -> Dict:
    """
    Generate actionable operational insights
    """
    insights = {
        'demand_patterns': {},
        'supply_optimization': {},
        'coverage_analysis': {},
        'recommendations': []
    }
    
    # Demand pattern insights
    peak_hours = df.groupby('delivery_hour_start')['order_id'].count().sort_values(ascending=False)
    peak_days = df.groupby('delivery_day_of_week')['order_id'].count().sort_values(ascending=False)
    
    insights['demand_patterns'] = {
        'peak_hour': peak_hours.index[0],
        'peak_hour_orders': peak_hours.iloc[0],
        'peak_day': peak_days.index[0],
        'peak_day_orders': peak_days.iloc[0],
        'total_orders': len(df),
        'avg_orders_per_hour': len(df) / 24,
        'avg_orders_per_day': len(df) / 7
    }
    
    # Supply optimization insights
    top_branches = branch_metrics.nlargest(5, 'total_orders')
    underutilized_branches = branch_metrics.nsmallest(5, 'total_orders')
    
    insights['supply_optimization'] = {
        'top_performing_branches': top_branches['branch_license'].tolist(),
        'underutilized_branches': underutilized_branches['branch_license'].tolist(),
        'avg_orders_per_branch': branch_metrics['total_orders'].mean(),
        'branch_utilization_std': branch_metrics['total_orders'].std()
    }
    
    # Coverage analysis
    if not coverage_gaps.empty:
        high_priority_gaps = coverage_gaps.nlargest(5, 'coverage_gap_score')
        insights['coverage_analysis'] = {
            'total_coverage_gaps': len(coverage_gaps),
            'high_priority_gaps': len(coverage_gaps[coverage_gaps['coverage_gap_score'] > coverage_gaps['coverage_gap_score'].median()]),
            'avg_gap_distance': coverage_gaps['distance_to_nearest_branch'].mean(),
            'total_underserved_orders': coverage_gaps['order_count'].sum()
        }
    
    # Generate recommendations
    recommendations = []
    
    # Peak hour staffing
    recommendations.append(f"Increase staffing during peak hour ({insights['demand_patterns']['peak_hour']}:00) with {insights['demand_patterns']['peak_hour_orders']} orders")
    
    # Branch optimization
    if insights['supply_optimization']['branch_utilization_std'] > insights['supply_optimization']['avg_orders_per_branch'] * 0.5:
        recommendations.append("High variation in branch utilization - consider redistributing resources")
    
    # Coverage gaps
    if 'coverage_analysis' in insights and insights['coverage_analysis']['total_coverage_gaps'] > 0:
        recommendations.append(f"Address {insights['coverage_analysis']['high_priority_gaps']} high-priority coverage gaps")
    
    insights['recommendations'] = recommendations
    
    return insights
