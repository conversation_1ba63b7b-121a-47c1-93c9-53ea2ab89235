# 📚 API Reference - Wasfaty Analytics

## Overview

This document provides detailed API documentation for all functions and classes in the Wasfaty Analytics package.

## 📦 Package Structure

```
src/
├── __init__.py
├── data_utils.py
└── geospatial_utils.py
```

## 🔧 data_utils.py

### Functions

#### `clean_merged_logs(file_path: str) -> pd.DataFrame`

Cleans merged log files by removing headers and file separators.

**Parameters:**
- `file_path` (str): Path to the merged logs CSV file

**Returns:**
- `pd.DataFrame`: Cleaned dataframe with proper column types

**Raises:**
- `FileNotFoundError`: If the specified file doesn't exist
- `pd.errors.EmptyDataError`: If the file is empty or corrupted

**Example:**
```python
from src.data_utils import clean_merged_logs
df = clean_merged_logs('data/raw/daily_logs/merged_logs.csv')
print(f"Loaded {len(df)} records")
```

#### `parse_timestamps(df: pd.DataFrame, timestamp_col: str = 'timestamp') -> pd.DataFrame`

Parses timestamp columns and extracts time-based features.

**Parameters:**
- `df` (pd.DataFrame): Input dataframe
- `timestamp_col` (str): Name of timestamp column (default: 'timestamp')

**Returns:**
- `pd.DataFrame`: Dataframe with additional time-based columns

**Example:**
```python
df = parse_timestamps(df)
# Adds columns: hour, day_of_week, month, quarter
```

#### `validate_coordinates(df: pd.DataFrame, lat_col: str = 'latitude', lon_col: str = 'longitude') -> pd.DataFrame`

Validates and cleans geographic coordinates.

**Parameters:**
- `df` (pd.DataFrame): Input dataframe
- `lat_col` (str): Latitude column name
- `lon_col` (str): Longitude column name

**Returns:**
- `pd.DataFrame`: Dataframe with validated coordinates

**Example:**
```python
df = validate_coordinates(df)
# Removes invalid coordinates and outliers
```

## 🗺️ geospatial_utils.py

### Functions

#### `add_h3_indices(df: pd.DataFrame, lat_col: str = 'latitude', lon_col: str = 'longitude', resolutions: List[int] = [7, 8, 9]) -> pd.DataFrame`

Adds H3 hexagonal indices at multiple resolutions for hierarchical spatial analysis.

**Parameters:**
- `df` (pd.DataFrame): DataFrame with latitude and longitude columns
- `lat_col` (str): Name of latitude column (default: 'latitude')
- `lon_col` (str): Name of longitude column (default: 'longitude')
- `resolutions` (List[int]): List of H3 resolutions to compute (default: [7, 8, 9])

**Returns:**
- `pd.DataFrame`: DataFrame with additional H3 index columns

**Example:**
```python
from src.geospatial_utils import add_h3_indices
df = add_h3_indices(df, resolutions=[7, 8, 9])
# Adds columns: h3_7, h3_8, h3_9
```

#### `create_delivery_heatmap(df: pd.DataFrame, lat_col: str = 'latitude', lon_col: str = 'longitude', output_path: str = None) -> folium.Map`

Creates an interactive heatmap of delivery locations.

**Parameters:**
- `df` (pd.DataFrame): DataFrame with delivery data
- `lat_col` (str): Latitude column name
- `lon_col` (str): Longitude column name
- `output_path` (str, optional): Path to save HTML file

**Returns:**
- `folium.Map`: Interactive Folium map object

**Example:**
```python
map_obj = create_delivery_heatmap(df, output_path='outputs/maps/heatmap.html')
```

#### `analyze_h3_demand(df: pd.DataFrame, h3_col: str = 'h3_7') -> pd.DataFrame`

Analyzes demand patterns by H3 hexagon.

**Parameters:**
- `df` (pd.DataFrame): DataFrame with H3 indices
- `h3_col` (str): H3 column to analyze (default: 'h3_7')

**Returns:**
- `pd.DataFrame`: Aggregated demand statistics by H3 cell

**Example:**
```python
h3_stats = analyze_h3_demand(df, h3_col='h3_7')
print(h3_stats.head())
```

#### `cluster_delivery_points(df: pd.DataFrame, lat_col: str = 'latitude', lon_col: str = 'longitude', method: str = 'DBSCAN', **kwargs) -> pd.DataFrame`

Clusters delivery points using specified algorithm.

**Parameters:**
- `df` (pd.DataFrame): DataFrame with coordinates
- `lat_col` (str): Latitude column name
- `lon_col` (str): Longitude column name
- `method` (str): Clustering method ('DBSCAN' or 'KMeans')
- `**kwargs`: Additional parameters for clustering algorithm

**Returns:**
- `pd.DataFrame`: DataFrame with cluster labels

**Example:**
```python
df_clustered = cluster_delivery_points(df, method='DBSCAN', eps=0.01, min_samples=5)
```

#### `calculate_coverage_metrics(df: pd.DataFrame, branches_df: pd.DataFrame, radius_km: float = 5.0) -> Dict`

Calculates coverage metrics for branch network.

**Parameters:**
- `df` (pd.DataFrame): Delivery data
- `branches_df` (pd.DataFrame): Branch location data
- `radius_km` (float): Coverage radius in kilometers

**Returns:**
- `Dict`: Coverage statistics and metrics

**Example:**
```python
coverage = calculate_coverage_metrics(df, branches_df, radius_km=3.0)
print(f"Coverage: {coverage['coverage_percentage']:.1f}%")
```

#### `optimize_delivery_zones(df: pd.DataFrame, n_zones: int = 10) -> Tuple[pd.DataFrame, Dict]`

Optimizes delivery zones using clustering and operational research methods.

**Parameters:**
- `df` (pd.DataFrame): Delivery data with coordinates
- `n_zones` (int): Number of zones to create

**Returns:**
- `Tuple[pd.DataFrame, Dict]`: Zone assignments and optimization metrics

**Example:**
```python
zones_df, metrics = optimize_delivery_zones(df, n_zones=8)
```

## 🎨 Visualization Functions

#### `create_choropleth_map(df: pd.DataFrame, value_col: str, geometry_col: str = 'h3_7') -> folium.Map`

Creates a choropleth map for geographic data visualization.

**Parameters:**
- `df` (pd.DataFrame): Data with geographic identifiers
- `value_col` (str): Column containing values to visualize
- `geometry_col` (str): Column containing geographic identifiers

**Returns:**
- `folium.Map`: Choropleth map object

#### `plot_temporal_patterns(df: pd.DataFrame, time_col: str = 'timestamp', value_col: str = 'order_count') -> go.Figure`

Creates interactive temporal pattern visualizations.

**Parameters:**
- `df` (pd.DataFrame): Time-series data
- `time_col` (str): Timestamp column
- `value_col` (str): Value column to plot

**Returns:**
- `go.Figure`: Plotly figure object

## 🔍 Utility Functions

#### `haversine_distance(lat1: float, lon1: float, lat2: float, lon2: float) -> float`

Calculates the great circle distance between two points on Earth.

**Parameters:**
- `lat1, lon1` (float): Coordinates of first point
- `lat2, lon2` (float): Coordinates of second point

**Returns:**
- `float`: Distance in kilometers

#### `get_h3_neighbors(h3_index: str, k: int = 1) -> List[str]`

Gets neighboring H3 cells at specified distance.

**Parameters:**
- `h3_index` (str): H3 index
- `k` (int): Distance (number of rings)

**Returns:**
- `List[str]`: List of neighboring H3 indices

## 📊 Constants and Configuration

### Default Parameters

```python
# H3 Resolutions
DEFAULT_H3_RESOLUTIONS = [7, 8, 9]

# Map Configuration
DEFAULT_MAP_CENTER = [24.7136, 46.6753]  # Riyadh
DEFAULT_ZOOM_LEVEL = 10

# Clustering Parameters
DEFAULT_DBSCAN_EPS = 0.01
DEFAULT_DBSCAN_MIN_SAMPLES = 5

# Color Schemes
HEATMAP_COLORMAP = 'YlOrRd'
CLUSTER_COLORS = ['red', 'blue', 'green', 'purple', 'orange', 'darkred', 'lightred', 'beige', 'darkblue', 'darkgreen']
```

## 🚨 Error Handling

### Common Exceptions

- `ValueError`: Invalid input parameters
- `KeyError`: Missing required columns
- `FileNotFoundError`: Data files not found
- `MemoryError`: Insufficient memory for large datasets

### Best Practices

1. Always validate input data before processing
2. Use try-catch blocks for file operations
3. Check for required columns before analysis
4. Handle edge cases (empty datasets, invalid coordinates)

## 📈 Performance Notes

### Memory Usage
- H3 indexing: ~50MB per million records
- Clustering: Memory scales with O(n²) for some algorithms
- Visualization: Large datasets may require sampling

### Optimization Tips
- Use appropriate H3 resolution (7-9 for city-level analysis)
- Sample large datasets for initial exploration
- Cache expensive computations
- Use vectorized operations when possible

---

For more examples and tutorials, see the User Guide and Developer Guide.
