# 🛠️ Developer Guide - Wasfaty Analytics

## 📋 Table of Contents

1. [Development Setup](#development-setup)
2. [Code Structure](#code-structure)
3. [API Documentation](#api-documentation)
4. [Contributing Guidelines](#contributing-guidelines)
5. [Testing](#testing)
6. [Code Style](#code-style)

## 🚀 Development Setup

### Environment Setup

```bash
# Clone the repository
git clone <repository-url>
cd wasfaty-analytics

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install development dependencies
pip install -e .[dev]

# Install pre-commit hooks (optional)
pre-commit install
```

### IDE Configuration

#### VS Code
Recommended extensions:
- Python
- Jupyter
- Black Formatter
- Pylance

#### PyCharm
- Enable Black formatter
- Configure pytest as test runner

## 🏗️ Code Structure

### Source Code Organization

```
src/
├── __init__.py           # Package initialization
├── data_utils.py         # Data processing utilities
└── geospatial_utils.py   # Geospatial analysis functions
```

### Module Dependencies

```mermaid
graph TD
    A[data_utils.py] --> B[geospatial_utils.py]
    B --> C[Notebooks]
    A --> C
```

## 📚 API Documentation

### data_utils.py

#### `clean_merged_logs(file_path: str) -> pd.DataFrame`
Cleans merged log files by removing headers and separators.

**Parameters:**
- `file_path`: Path to the merged logs CSV file

**Returns:**
- Cleaned pandas DataFrame

**Example:**
```python
from src.data_utils import clean_merged_logs
df = clean_merged_logs('data/raw/daily_logs/merged_logs.csv')
```

### geospatial_utils.py

#### `add_h3_indices(df: pd.DataFrame, lat_col: str, lon_col: str, resolutions: List[int]) -> pd.DataFrame`
Adds H3 indices at multiple resolutions for hierarchical spatial analysis.

**Parameters:**
- `df`: DataFrame with latitude and longitude columns
- `lat_col`: Name of latitude column (default: 'latitude')
- `lon_col`: Name of longitude column (default: 'longitude')
- `resolutions`: List of H3 resolutions to compute (default: [7, 8, 9])

**Returns:**
- DataFrame with additional H3 index columns

## 🤝 Contributing Guidelines

### Workflow

1. **Fork** the repository
2. **Create** a feature branch: `git checkout -b feature/amazing-feature`
3. **Make** your changes
4. **Add** tests for new functionality
5. **Run** tests: `pytest tests/`
6. **Format** code: `black src/ tests/`
7. **Lint** code: `flake8 src/ tests/`
8. **Commit** changes: `git commit -m 'Add amazing feature'`
9. **Push** to branch: `git push origin feature/amazing-feature`
10. **Submit** a Pull Request

### Commit Message Convention

Use conventional commits:
- `feat:` New feature
- `fix:` Bug fix
- `docs:` Documentation changes
- `style:` Code style changes
- `refactor:` Code refactoring
- `test:` Adding tests
- `chore:` Maintenance tasks

Example: `feat: add H3 clustering analysis function`

### Code Review Process

1. All PRs require at least one review
2. Automated tests must pass
3. Code coverage should not decrease
4. Documentation must be updated for new features

## 🧪 Testing

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=src

# Run specific test file
pytest tests/test_data_utils.py

# Run with verbose output
pytest -v
```

### Writing Tests

Create test files in the `tests/` directory:

```python
# tests/test_new_feature.py
import pytest
from src.data_utils import new_function

def test_new_function():
    """Test the new function works correctly."""
    result = new_function(input_data)
    assert result == expected_output

def test_new_function_edge_case():
    """Test edge cases."""
    with pytest.raises(ValueError):
        new_function(invalid_input)
```

### Test Coverage

Maintain minimum 80% test coverage:
- Unit tests for all utility functions
- Integration tests for notebook workflows
- Mock external dependencies

## 🎨 Code Style

### Python Style Guide

Follow PEP 8 with these specifics:
- Line length: 88 characters (Black default)
- Use type hints for function signatures
- Docstrings in Google style

### Example Function

```python
def process_delivery_data(
    df: pd.DataFrame,
    date_column: str = "delivery_date",
    location_columns: Tuple[str, str] = ("latitude", "longitude")
) -> pd.DataFrame:
    """
    Process delivery data for analysis.
    
    Args:
        df: Input DataFrame with delivery data
        date_column: Name of the date column
        location_columns: Tuple of (latitude, longitude) column names
    
    Returns:
        Processed DataFrame with additional features
        
    Raises:
        ValueError: If required columns are missing
    """
    # Implementation here
    pass
```

### Documentation Style

Use Google-style docstrings:

```python
def function_name(param1: type, param2: type) -> return_type:
    """
    Brief description of the function.
    
    Longer description if needed. Explain the purpose,
    algorithm, or important details.
    
    Args:
        param1: Description of parameter 1
        param2: Description of parameter 2
    
    Returns:
        Description of return value
        
    Raises:
        ExceptionType: Description of when this exception is raised
        
    Example:
        >>> result = function_name("value1", 42)
        >>> print(result)
        Expected output
    """
```

## 🔧 Development Tools

### Useful Commands

```bash
# Format code
black src/ tests/ notebooks/

# Lint code
flake8 src/ tests/

# Type checking
mypy src/

# Security check
bandit -r src/

# Dependency check
pip-audit
```

### Pre-commit Hooks

Create `.pre-commit-config.yaml`:

```yaml
repos:
  - repo: https://github.com/psf/black
    rev: 22.3.0
    hooks:
      - id: black
  - repo: https://github.com/pycqa/flake8
    rev: 4.0.1
    hooks:
      - id: flake8
```

## 📊 Performance Guidelines

- Use vectorized operations with pandas/numpy
- Profile code with `cProfile` for bottlenecks
- Cache expensive computations
- Use appropriate data types (e.g., category for strings)
- Consider memory usage for large datasets

## 🐛 Debugging

### Common Issues

1. **Import Errors**: Check PYTHONPATH and virtual environment
2. **Memory Issues**: Use chunking for large datasets
3. **Geospatial Errors**: Verify coordinate systems and projections

### Debugging Tools

- Use `pdb` for interactive debugging
- Add logging with appropriate levels
- Use Jupyter debugger for notebook development

---

For questions about development, create an issue or contact the development team.
