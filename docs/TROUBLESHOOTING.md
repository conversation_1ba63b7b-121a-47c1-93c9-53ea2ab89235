# 🔧 Troubleshooting Guide - Wasfaty Analytics

## Common Issues and Solutions

### 🚨 Installation Issues

#### Problem: "Module not found" errors
```
ModuleNotFoundError: No module named 'h3'
```

**Solution:**
```bash
# Ensure virtual environment is activated
source venv/bin/activate  # macOS/Linux
# or
venv\Scripts\activate  # Windows

# Reinstall requirements
pip install -r requirements.txt
```

#### Problem: Permission denied during installation
```
ERROR: Could not install packages due to an EnvironmentError: [Errno 13] Permission denied
```

**Solution:**
```bash
# Use --user flag or virtual environment
pip install --user -r requirements.txt
# or create virtual environment
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

### 📊 Data Issues

#### Problem: "File not found" when loading data
```
FileNotFoundError: [Errno 2] No such file or directory: 'daily_logs/merged_logs.csv'
```

**Solution:**
1. Check file location: `data/raw/daily_logs/merged_logs.csv`
2. Verify file name spelling
3. Update notebook path if needed:
   ```python
   file_path = 'data/raw/daily_logs/merged_logs.csv'
   ```

#### Problem: Empty or corrupted data files
```
pd.errors.EmptyDataError: No columns to parse from file
```

**Solution:**
1. Check file content: `head data/raw/daily_logs/merged_logs.csv`
2. Verify CSV format and headers
3. Re-download or regenerate data files

#### Problem: Invalid coordinates
```
ValueError: Invalid latitude/longitude values
```

**Solution:**
```python
# Add data validation
df = df[(df['latitude'].between(-90, 90)) & 
        (df['longitude'].between(-180, 180))]
```

### 🗺️ Geospatial Issues

#### Problem: H3 indexing fails
```
ValueError: Invalid lat/lng
```

**Solution:**
```python
# Clean coordinates first
from src.data_utils import validate_coordinates
df = validate_coordinates(df)
```

#### Problem: Maps not displaying
**Symptoms:** Blank maps or JavaScript errors

**Solutions:**
1. **Internet connection:** Maps require online tile servers
2. **Browser compatibility:** Use Chrome, Firefox, or Safari
3. **File path issues:**
   ```python
   # Use absolute paths for map files
   import os
   output_path = os.path.abspath('outputs/maps/heatmap.html')
   ```

#### Problem: Memory errors with large datasets
```
MemoryError: Unable to allocate array
```

**Solution:**
```python
# Use sampling for large datasets
df_sample = df.sample(n=100000, random_state=42)

# Or process in chunks
chunk_size = 50000
for chunk in pd.read_csv('large_file.csv', chunksize=chunk_size):
    # Process chunk
    pass
```

### 📈 Visualization Issues

#### Problem: Plotly figures not showing in Jupyter
**Solution:**
```python
# Install Jupyter extensions
pip install jupyterlab-plotly

# Or use different renderer
import plotly.io as pio
pio.renderers.default = "browser"
```

#### Problem: Folium maps not interactive
**Solution:**
```python
# Ensure proper map initialization
import folium
m = folium.Map(location=[24.7136, 46.6753], zoom_start=10)
# Save and open in browser
m.save('test_map.html')
```

### 🔄 Performance Issues

#### Problem: Slow notebook execution
**Solutions:**
1. **Reduce data size:**
   ```python
   # Sample data for testing
   df_test = df.sample(n=10000)
   ```

2. **Lower H3 resolution:**
   ```python
   # Use resolution 6-7 instead of 8-9
   resolutions = [6, 7]
   ```

3. **Cache results:**
   ```python
   # Save intermediate results
   df.to_pickle('processed_data.pkl')
   ```

#### Problem: Jupyter kernel crashes
**Solutions:**
1. **Increase memory limit:**
   ```bash
   jupyter lab --NotebookApp.max_buffer_size=1000000000
   ```

2. **Restart kernel regularly:**
   - Kernel → Restart & Clear Output

3. **Use smaller chunks:**
   ```python
   # Process data in smaller batches
   batch_size = 10000
   ```

### 🐍 Python Environment Issues

#### Problem: Wrong Python version
```
SyntaxError: f-strings require Python 3.6+
```

**Solution:**
```bash
# Check Python version
python --version
# Should be 3.8 or higher

# Use specific Python version
python3.8 -m venv venv
```

#### Problem: Package conflicts
```
ImportError: cannot import name 'something' from 'package'
```

**Solution:**
```bash
# Create fresh environment
rm -rf venv
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

### 📝 Notebook Issues

#### Problem: Kernel not connecting
**Solutions:**
1. **Restart Jupyter:**
   ```bash
   # Stop Jupyter (Ctrl+C)
   # Restart
   jupyter lab
   ```

2. **Check kernel list:**
   ```bash
   jupyter kernelspec list
   ```

3. **Install kernel:**
   ```bash
   python -m ipykernel install --user --name=wasfaty-env
   ```

#### Problem: Cells not executing
**Solutions:**
1. **Check for infinite loops**
2. **Restart kernel:** Kernel → Restart
3. **Clear output:** Cell → All Output → Clear

### 🔐 Permission Issues

#### Problem: Cannot write output files
```
PermissionError: [Errno 13] Permission denied: 'outputs/maps/heatmap.html'
```

**Solution:**
```bash
# Check directory permissions
ls -la outputs/
# Create directories if needed
mkdir -p outputs/maps outputs/kml outputs/reports
# Fix permissions
chmod 755 outputs/
```

### 🌐 Network Issues

#### Problem: Cannot download map tiles
**Symptoms:** Maps show gray tiles or don't load

**Solutions:**
1. **Check internet connection**
2. **Try different tile server:**
   ```python
   folium.Map(
       tiles='OpenStreetMap',  # or 'CartoDB positron'
       location=[24.7136, 46.6753]
   )
   ```

3. **Use offline mode:**
   ```python
   # Save map data locally
   m.save('map.html')
   ```

## 🆘 Getting Help

### Before Asking for Help

1. **Check this troubleshooting guide**
2. **Search existing issues**
3. **Try the solution on a small dataset**
4. **Check the logs for error messages**

### Creating a Bug Report

Include:
1. **Error message** (full traceback)
2. **Steps to reproduce**
3. **Environment info:**
   ```bash
   python --version
   pip list | grep -E "(pandas|numpy|folium|h3)"
   ```
4. **Sample data** (anonymized)
5. **Expected vs actual behavior**

### Contact Information

- **Documentation:** Check `docs/` folder
- **Examples:** Review notebook examples
- **Issues:** Create detailed issue reports

## 🔄 Recovery Procedures

### Corrupted Environment
```bash
# Complete reset
rm -rf venv
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

### Lost Work
```bash
# Check for backup files
ls -la *.backup
# Restore from backup
cp notebook.ipynb.backup notebook.ipynb
```

### Performance Reset
```python
# Clear all variables
%reset -f
# Restart kernel
# Re-run essential imports only
```

---

**Remember:** Most issues can be resolved by restarting the kernel, checking file paths, and ensuring the virtual environment is properly activated.
