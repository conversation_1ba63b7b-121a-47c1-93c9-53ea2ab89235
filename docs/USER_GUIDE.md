# 📖 User Guide - Wasfaty Analytics

## 🎯 Getting Started

Welcome to Wasfaty Analytics! This guide will help you get up and running with analyzing your pharmacy delivery data.

## 📋 Table of Contents

1. [Installation](#installation)
2. [Data Preparation](#data-preparation)
3. [Running Your First Analysis](#running-your-first-analysis)
4. [Understanding the Outputs](#understanding-the-outputs)
5. [Advanced Features](#advanced-features)
6. [Troubleshooting](#troubleshooting)

## 🚀 Installation

### Step 1: System Requirements

Ensure you have:
- Python 3.8 or higher
- At least 4GB RAM (8GB recommended for large datasets)
- 2GB free disk space

### Step 2: Download and Setup

```bash
# Navigate to your project directory
cd /path/to/your/project

# Create a virtual environment
python -m venv wasfaty-env

# Activate the environment
# On macOS/Linux:
source wasfaty-env/bin/activate
# On Windows:
wasfaty-env\Scripts\activate

# Install the package
pip install -r requirements.txt
```

### Step 3: Verify Installation

```bash
# Start Jupyter Lab
jupyter lab

# You should see the Jupyter interface open in your browser
```

## 📊 Data Preparation

### Required Data Format

Your data should be in CSV format with these columns:

| Column | Description | Example |
|--------|-------------|---------|
| `timestamp` | Order timestamp | 2025-06-01 14:30:00 |
| `branch_license` | Branch identifier | BR001 |
| `delivery_date` | Date of delivery | 2025-06-01 |
| `latitude` | Delivery latitude | 24.7136 |
| `longitude` | Delivery longitude | 46.6753 |
| `area` | Area name | Riyadh Central |
| `order_id` | Unique order ID | ORD123456 |

### Data Placement

1. Place your CSV files in the `data/raw/daily_logs/` folder
2. Name your main file `merged_logs.csv`
3. Individual daily files can be named `logs_YYYY-MM-DD.csv`

## 🔍 Running Your First Analysis

### Option 1: Quick Start (Recommended for Beginners)

1. **Open Jupyter Lab**
   ```bash
   jupyter lab
   ```

2. **Navigate to Exploratory Analysis**
   - Go to `notebooks/exploratory/`
   - Open `wasfaty_analytics.ipynb`

3. **Run the Analysis**
   - Click "Run All Cells" or press `Shift + Enter` for each cell
   - Follow the step-by-step instructions in the notebook

### Option 2: Focused Analysis

Choose based on your needs:

#### Geospatial Analysis
- **File**: `notebooks/geospatial/advanced_geospatial_analysis.ipynb`
- **Purpose**: Find delivery hotspots and optimize coverage
- **Best for**: Operations teams, territory planning

#### Temporal Analysis
- **File**: `notebooks/temporal/enhanced_temporal_analysis.ipynb`
- **Purpose**: Understand demand patterns over time
- **Best for**: Capacity planning, staffing decisions

#### Executive Summary
- **File**: `notebooks/executive/executive_summary_presentation.ipynb`
- **Purpose**: Generate stakeholder-ready reports
- **Best for**: Management presentations, KPI tracking

## 📈 Understanding the Outputs

### Interactive Maps

Your analysis will generate several types of maps:

#### 1. Delivery Heatmap
- **File**: `outputs/maps/delivery_heatmap.html`
- **Shows**: Concentration of deliveries across the city
- **Use**: Identify high-demand areas

#### 2. Branch Coverage Map
- **File**: `outputs/maps/branch_distribution_map.html`
- **Shows**: Current branch locations and their coverage
- **Use**: Assess coverage gaps

#### 3. H3 Hexagon Analysis
- **File**: `outputs/maps/h3_delivery_density_res7.html`
- **Shows**: Demand aggregated into hexagonal zones
- **Use**: Strategic planning and resource allocation

### KML Files (Google Earth)

- **Location**: `outputs/kml/`
- **Purpose**: View analysis results in Google Earth
- **Files**:
  - `wasfaty_top30_branches.kml`: Top performing branches
  - `wasfaty_all_branches_by_tier.kml`: All branches categorized

### Reports and Charts

- **Location**: `outputs/reports/`
- **Includes**: Executive summaries, trend analysis, recommendations

## 🎛️ Advanced Features

### Custom Analysis Parameters

You can modify analysis parameters by editing configuration files:

```python
# In your notebook, customize these settings:
H3_RESOLUTION = 7  # Higher = more detailed (6-10 recommended)
TIME_PERIODS = ['morning', 'afternoon', 'evening', 'night']
CLUSTER_METHOD = 'DBSCAN'  # or 'KMeans'
```

### Batch Processing

For multiple datasets:

```bash
# Use the batch processing script
python scripts/run_geospatial_analysis.py --input-dir data/raw/ --output-dir outputs/
```

### Custom Visualizations

Modify visualization parameters:

```python
# In notebooks, adjust these settings:
MAP_CENTER = [24.7136, 46.6753]  # Riyadh coordinates
ZOOM_LEVEL = 10
COLOR_SCHEME = 'viridis'  # or 'plasma', 'inferno'
```

## 🔧 Troubleshooting

### Common Issues

#### 1. "Module not found" Error
```bash
# Solution: Ensure virtual environment is activated
source wasfaty-env/bin/activate  # macOS/Linux
# or
wasfaty-env\Scripts\activate  # Windows
```

#### 2. "File not found" Error
- Check that your data files are in `data/raw/daily_logs/`
- Verify file names match expected format
- Ensure CSV files have proper headers

#### 3. Memory Issues with Large Datasets
```python
# In notebooks, use chunking:
chunk_size = 10000
for chunk in pd.read_csv('large_file.csv', chunksize=chunk_size):
    # Process chunk
    pass
```

#### 4. Maps Not Displaying
- Ensure you have internet connection (maps use online tiles)
- Try opening HTML files directly in browser
- Check browser console for JavaScript errors

### Performance Tips

#### For Large Datasets (>1M records):
1. **Use sampling** for initial exploration:
   ```python
   df_sample = df.sample(n=100000)  # Use 100k records
   ```

2. **Enable chunked processing**:
   ```python
   ENABLE_CHUNKING = True
   CHUNK_SIZE = 50000
   ```

3. **Reduce H3 resolution**:
   ```python
   H3_RESOLUTION = 6  # Less detailed but faster
   ```

### Getting Help

#### 1. Check Documentation
- Review this user guide
- Check the developer guide for technical details
- Look at example notebooks

#### 2. Common Solutions
- Restart Jupyter kernel: `Kernel > Restart`
- Clear output and re-run: `Cell > All Output > Clear`
- Update packages: `pip install -r requirements.txt --upgrade`

#### 3. Contact Support
If you're still having issues:
- Create a detailed issue report
- Include error messages and steps to reproduce
- Attach sample data (anonymized)

## 📚 Learning Resources

### Tutorials
1. **Basic Analysis**: Start with `notebooks/exploratory/wasfaty_analytics.ipynb`
2. **Geospatial Concepts**: Review H3 documentation at h3geo.org
3. **Data Visualization**: Explore Folium and Plotly documentation

### Best Practices
- Always backup your data before processing
- Start with small datasets to understand the workflow
- Document your analysis steps and findings
- Share insights with your team regularly

### Next Steps
Once comfortable with basic analysis:
1. Explore advanced geospatial features
2. Create custom visualizations
3. Automate regular reporting
4. Integrate with business intelligence tools

---

**Happy analyzing! 🚀**

For additional support, contact the Wasfaty Analytics team.
