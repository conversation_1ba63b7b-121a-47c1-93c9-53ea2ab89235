# 🤝 Contributing to Wasfaty Analytics

Thank you for your interest in contributing to Wasfaty Analytics! This document provides guidelines for contributing to the project.

## 📋 Table of Contents

1. [Code of Conduct](#code-of-conduct)
2. [Getting Started](#getting-started)
3. [Development Workflow](#development-workflow)
4. [Coding Standards](#coding-standards)
5. [Testing Guidelines](#testing-guidelines)
6. [Documentation](#documentation)
7. [Submitting Changes](#submitting-changes)

## 🤝 Code of Conduct

### Our Pledge

We are committed to making participation in this project a harassment-free experience for everyone, regardless of age, body size, disability, ethnicity, gender identity and expression, level of experience, nationality, personal appearance, race, religion, or sexual identity and orientation.

### Our Standards

**Positive behavior includes:**
- Using welcoming and inclusive language
- Being respectful of differing viewpoints and experiences
- Gracefully accepting constructive criticism
- Focusing on what is best for the community
- Showing empathy towards other community members

**Unacceptable behavior includes:**
- The use of sexualized language or imagery
- Trolling, insulting/derogatory comments, and personal or political attacks
- Public or private harassment
- Publishing others' private information without explicit permission

## 🚀 Getting Started

### Prerequisites

- Python 3.8 or higher
- Git
- Basic knowledge of data analysis and geospatial concepts
- Familiarity with Jupyter notebooks

### Development Setup

1. **Fork the repository**
   ```bash
   # Fork on GitHub, then clone your fork
   git clone https://github.com/YOUR_USERNAME/wasfaty-analytics.git
   cd wasfaty-analytics
   ```

2. **Set up development environment**
   ```bash
   # Create virtual environment
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   
   # Install development dependencies
   pip install -e .[dev]
   
   # Install pre-commit hooks (optional but recommended)
   pre-commit install
   ```

3. **Verify setup**
   ```bash
   # Run tests
   pytest tests/
   
   # Start Jupyter
   jupyter lab
   ```

## 🔄 Development Workflow

### Branch Strategy

- `main`: Production-ready code
- `develop`: Integration branch for features
- `feature/feature-name`: New features
- `bugfix/bug-description`: Bug fixes
- `hotfix/critical-fix`: Critical production fixes

### Workflow Steps

1. **Create a feature branch**
   ```bash
   git checkout -b feature/amazing-new-feature
   ```

2. **Make your changes**
   - Write code following our standards
   - Add tests for new functionality
   - Update documentation

3. **Test your changes**
   ```bash
   # Run tests
   pytest tests/
   
   # Run linting
   flake8 src/ tests/
   
   # Format code
   black src/ tests/
   ```

4. **Commit your changes**
   ```bash
   git add .
   git commit -m "feat: add amazing new feature"
   ```

5. **Push and create PR**
   ```bash
   git push origin feature/amazing-new-feature
   # Create Pull Request on GitHub
   ```

## 📝 Coding Standards

### Python Style Guide

We follow PEP 8 with these specific guidelines:

#### Code Formatting
- **Line length**: 88 characters (Black default)
- **Indentation**: 4 spaces
- **Quotes**: Double quotes for strings
- **Imports**: Organized using isort

#### Naming Conventions
```python
# Variables and functions: snake_case
delivery_count = 100
def calculate_distance():
    pass

# Classes: PascalCase
class DeliveryAnalyzer:
    pass

# Constants: UPPER_SNAKE_CASE
MAX_DELIVERY_RADIUS = 10.0

# Private methods: _leading_underscore
def _internal_helper():
    pass
```

#### Type Hints
Always use type hints for function signatures:

```python
from typing import List, Dict, Optional, Tuple
import pandas as pd

def process_delivery_data(
    df: pd.DataFrame,
    date_column: str = "delivery_date",
    coordinates: Tuple[str, str] = ("latitude", "longitude")
) -> pd.DataFrame:
    """Process delivery data with proper type hints."""
    pass
```

#### Docstrings
Use Google-style docstrings:

```python
def calculate_h3_demand(
    df: pd.DataFrame, 
    h3_col: str = 'h3_7'
) -> pd.DataFrame:
    """
    Calculate demand metrics by H3 hexagon.
    
    This function aggregates delivery data by H3 hexagon and calculates
    various demand metrics including count, density, and temporal patterns.
    
    Args:
        df: DataFrame containing delivery data with H3 indices
        h3_col: Column name containing H3 indices
        
    Returns:
        DataFrame with aggregated demand metrics by H3 cell
        
    Raises:
        KeyError: If h3_col is not found in the DataFrame
        ValueError: If DataFrame is empty
        
    Example:
        >>> df_with_h3 = add_h3_indices(delivery_df)
        >>> demand_stats = calculate_h3_demand(df_with_h3)
        >>> print(demand_stats.head())
    """
    pass
```

### Code Organization

#### File Structure
```python
# Standard library imports
import os
import sys
from datetime import datetime
from typing import List, Dict

# Third-party imports
import pandas as pd
import numpy as np
import folium

# Local imports
from src.data_utils import clean_merged_logs
from src.geospatial_utils import add_h3_indices
```

#### Function Design
- **Single responsibility**: Each function should do one thing well
- **Pure functions**: Avoid side effects when possible
- **Error handling**: Use appropriate exceptions
- **Input validation**: Check parameters at function start

```python
def validate_coordinates(
    df: pd.DataFrame, 
    lat_col: str = 'latitude', 
    lon_col: str = 'longitude'
) -> pd.DataFrame:
    """Validate and clean coordinate data."""
    if df.empty:
        raise ValueError("DataFrame cannot be empty")
    
    if lat_col not in df.columns:
        raise KeyError(f"Column '{lat_col}' not found")
    
    # Validation logic here
    return cleaned_df
```

## 🧪 Testing Guidelines

### Test Structure

```
tests/
├── __init__.py
├── test_data_utils.py
├── test_geospatial_utils.py
├── fixtures/
│   ├── sample_data.csv
│   └── test_config.json
└── integration/
    └── test_full_workflow.py
```

### Writing Tests

#### Unit Tests
```python
import pytest
import pandas as pd
from src.data_utils import clean_merged_logs

class TestDataUtils:
    """Test suite for data utility functions."""
    
    def test_clean_merged_logs_valid_file(self):
        """Test cleaning with valid input file."""
        # Arrange
        test_file = "tests/fixtures/sample_data.csv"
        
        # Act
        result = clean_merged_logs(test_file)
        
        # Assert
        assert isinstance(result, pd.DataFrame)
        assert len(result) > 0
        assert 'timestamp' in result.columns
    
    def test_clean_merged_logs_missing_file(self):
        """Test error handling for missing file."""
        with pytest.raises(FileNotFoundError):
            clean_merged_logs("nonexistent_file.csv")
    
    @pytest.mark.parametrize("lat,lon,expected", [
        (24.7136, 46.6753, True),   # Valid Riyadh coordinates
        (91.0, 46.6753, False),     # Invalid latitude
        (24.7136, 181.0, False),    # Invalid longitude
    ])
    def test_coordinate_validation(self, lat, lon, expected):
        """Test coordinate validation with various inputs."""
        df = pd.DataFrame({'latitude': [lat], 'longitude': [lon]})
        result = validate_coordinates(df)
        assert (len(result) > 0) == expected
```

#### Integration Tests
```python
def test_full_geospatial_workflow():
    """Test complete geospatial analysis workflow."""
    # Load test data
    df = pd.read_csv("tests/fixtures/sample_data.csv")
    
    # Run full workflow
    df = clean_merged_logs(df)
    df = add_h3_indices(df)
    demand_stats = calculate_h3_demand(df)
    
    # Verify results
    assert len(demand_stats) > 0
    assert 'demand_count' in demand_stats.columns
```

### Test Coverage

Maintain minimum 80% test coverage:
```bash
# Run tests with coverage
pytest --cov=src --cov-report=html

# View coverage report
open htmlcov/index.html
```

## 📚 Documentation

### Documentation Types

1. **Code Documentation**: Docstrings and comments
2. **User Documentation**: Tutorials and guides
3. **API Documentation**: Function and class references
4. **Developer Documentation**: Architecture and contributing guides

### Writing Guidelines

#### User Documentation
- **Clear and concise**: Use simple language
- **Step-by-step**: Provide detailed instructions
- **Examples**: Include code examples and expected outputs
- **Screenshots**: Add visuals for complex procedures

#### API Documentation
- **Complete**: Document all public functions and classes
- **Accurate**: Keep documentation in sync with code
- **Examples**: Provide usage examples
- **Parameters**: Describe all parameters and return values

### Documentation Updates

When making changes:
1. **Update docstrings** for modified functions
2. **Update user guides** if workflow changes
3. **Add examples** for new features
4. **Update API reference** for new functions

## 📤 Submitting Changes

### Pull Request Process

1. **Ensure tests pass**
   ```bash
   pytest tests/
   flake8 src/ tests/
   black --check src/ tests/
   ```

2. **Update documentation**
   - Add docstrings for new functions
   - Update user guides if needed
   - Add examples for new features

3. **Create descriptive PR**
   - **Title**: Brief description of changes
   - **Description**: Detailed explanation of what and why
   - **Testing**: How you tested the changes
   - **Screenshots**: For UI/visualization changes

### PR Template

```markdown
## Description
Brief description of changes and motivation.

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update

## Testing
- [ ] Tests pass locally
- [ ] Added tests for new functionality
- [ ] Manual testing completed

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] No new warnings introduced
```

### Review Process

1. **Automated checks**: CI/CD pipeline runs tests
2. **Code review**: At least one reviewer required
3. **Documentation review**: Check for completeness
4. **Testing verification**: Ensure tests are adequate

### Merge Requirements

- ✅ All tests pass
- ✅ Code review approved
- ✅ Documentation updated
- ✅ No merge conflicts
- ✅ Branch up to date with main

## 🎯 Contribution Ideas

### Good First Issues
- Fix typos in documentation
- Add unit tests for existing functions
- Improve error messages
- Add examples to notebooks

### Feature Requests
- New visualization types
- Additional geospatial analysis methods
- Performance optimizations
- Integration with external APIs

### Bug Reports
- Use the issue template
- Provide minimal reproduction case
- Include environment details
- Attach relevant logs/screenshots

---

Thank you for contributing to Wasfaty Analytics! Your contributions help make pharmacy delivery operations more efficient and data-driven. 🚀
