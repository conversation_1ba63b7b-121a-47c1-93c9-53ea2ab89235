"""
Basic functionality tests for Wasfaty Analytics
"""

import pytest
import pandas as pd
import numpy as np
from pathlib import Path
import sys

# Add src to path for testing
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

try:
    from data_utils import clean_merged_logs
    from geospatial_utils import add_h3_indices
except ImportError as e:
    pytest.skip(f"Could not import modules: {e}", allow_module_level=True)


class TestBasicFunctionality:
    """Test basic functionality of the reorganized codebase."""
    
    def test_imports_work(self):
        """Test that basic imports work correctly."""
        # This test passes if imports above succeeded
        assert True
    
    def test_data_utils_functions_exist(self):
        """Test that data_utils functions are accessible."""
        assert callable(clean_merged_logs)
    
    def test_geospatial_utils_functions_exist(self):
        """Test that geospatial_utils functions are accessible."""
        assert callable(add_h3_indices)
    
    def test_sample_dataframe_processing(self):
        """Test basic DataFrame processing."""
        # Create sample data
        sample_data = {
            'timestamp': ['2025-06-01 10:00:00', '2025-06-01 11:00:00'],
            'latitude': [24.7136, 24.7200],
            'longitude': [46.6753, 46.6800],
            'order_id': ['ORD001', 'ORD002'],
            'area': ['Riyadh Central', 'Riyadh North']
        }
        df = pd.DataFrame(sample_data)
        
        # Test basic operations
        assert len(df) == 2
        assert 'latitude' in df.columns
        assert 'longitude' in df.columns
        
        # Test coordinate validation
        lat_valid = df['latitude'].between(-90, 90).all()
        lon_valid = df['longitude'].between(-180, 180).all()
        assert lat_valid and lon_valid
    
    def test_h3_indexing_basic(self):
        """Test basic H3 indexing functionality."""
        # Create sample data with valid coordinates
        sample_data = {
            'latitude': [24.7136, 24.7200],
            'longitude': [46.6753, 46.6800]
        }
        df = pd.DataFrame(sample_data)
        
        try:
            # Test H3 indexing
            df_with_h3 = add_h3_indices(df, resolutions=[7])
            assert 'h3_7' in df_with_h3.columns
            assert len(df_with_h3) == 2
        except Exception as e:
            # If H3 is not available, skip this test
            pytest.skip(f"H3 indexing not available: {e}")


class TestProjectStructure:
    """Test that the project structure is correctly organized."""
    
    def test_directory_structure(self):
        """Test that required directories exist."""
        project_root = Path(__file__).parent.parent
        
        required_dirs = [
            'src',
            'notebooks',
            'data',
            'outputs',
            'docs',
            'tests',
            'config',
            'scripts'
        ]
        
        for dir_name in required_dirs:
            dir_path = project_root / dir_name
            assert dir_path.exists(), f"Directory {dir_name} should exist"
            assert dir_path.is_dir(), f"{dir_name} should be a directory"
    
    def test_src_files_exist(self):
        """Test that source files exist in src directory."""
        project_root = Path(__file__).parent.parent
        src_dir = project_root / 'src'
        
        required_files = [
            '__init__.py',
            'data_utils.py',
            'geospatial_utils.py'
        ]
        
        for file_name in required_files:
            file_path = src_dir / file_name
            assert file_path.exists(), f"File {file_name} should exist in src/"
            assert file_path.is_file(), f"{file_name} should be a file"
    
    def test_documentation_exists(self):
        """Test that documentation files exist."""
        project_root = Path(__file__).parent.parent
        
        # Check main README
        main_readme = project_root / 'README.md'
        assert main_readme.exists(), "Main README.md should exist"
        
        # Check docs directory
        docs_dir = project_root / 'docs'
        assert docs_dir.exists(), "docs/ directory should exist"
        
        # Check for key documentation files
        doc_files = [
            'USER_GUIDE.md',
            'DEVELOPER_GUIDE.md',
            'API_REFERENCE.md',
            'TROUBLESHOOTING.md',
            'PROJECT_OVERVIEW.md'
        ]
        
        for doc_file in doc_files:
            doc_path = docs_dir / doc_file
            assert doc_path.exists(), f"Documentation file {doc_file} should exist"
    
    def test_notebooks_organized(self):
        """Test that notebooks are properly organized."""
        project_root = Path(__file__).parent.parent
        notebooks_dir = project_root / 'notebooks'
        
        required_subdirs = [
            'geospatial',
            'temporal',
            'executive',
            'exploratory'
        ]
        
        for subdir in required_subdirs:
            subdir_path = notebooks_dir / subdir
            assert subdir_path.exists(), f"Notebook subdirectory {subdir} should exist"
            assert subdir_path.is_dir(), f"{subdir} should be a directory"
    
    def test_outputs_structure(self):
        """Test that outputs directory is properly structured."""
        project_root = Path(__file__).parent.parent
        outputs_dir = project_root / 'outputs'
        
        required_subdirs = [
            'maps',
            'kml',
            'reports',
            'images'
        ]
        
        for subdir in required_subdirs:
            subdir_path = outputs_dir / subdir
            assert subdir_path.exists(), f"Output subdirectory {subdir} should exist"
            assert subdir_path.is_dir(), f"{subdir} should be a directory"


class TestConfiguration:
    """Test configuration and setup files."""
    
    def test_requirements_file(self):
        """Test that requirements.txt exists and is valid."""
        project_root = Path(__file__).parent.parent
        requirements_file = project_root / 'requirements.txt'
        
        assert requirements_file.exists(), "requirements.txt should exist"
        
        # Read and validate requirements
        with open(requirements_file, 'r') as f:
            content = f.read()
            
        # Check for essential packages
        essential_packages = ['pandas', 'numpy', 'folium', 'h3', 'jupyter']
        for package in essential_packages:
            assert package in content, f"Package {package} should be in requirements.txt"
    
    def test_setup_file(self):
        """Test that setup.py exists."""
        project_root = Path(__file__).parent.parent
        setup_file = project_root / 'setup.py'
        
        assert setup_file.exists(), "setup.py should exist"
    
    def test_contributing_guide(self):
        """Test that contributing guide exists."""
        project_root = Path(__file__).parent.parent
        contributing_file = project_root / 'CONTRIBUTING.md'
        
        assert contributing_file.exists(), "CONTRIBUTING.md should exist"


if __name__ == '__main__':
    # Run tests if script is executed directly
    pytest.main([__file__, '-v'])
