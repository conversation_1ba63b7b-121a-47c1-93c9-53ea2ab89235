#!/usr/bin/env python3
"""
Test script to verify all notebooks and analyses run without errors
"""

import subprocess
import sys
import os
from datetime import datetime

def run_command(command, description):
    """Run a command and return success status"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=300)
        if result.returncode == 0:
            print(f"✅ {description} - SUCCESS")
            return True
        else:
            print(f"❌ {description} - FAILED")
            print(f"Error: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} - TIMEOUT")
        return False
    except Exception as e:
        print(f"❌ {description} - ERROR: {str(e)}")
        return False

def main():
    """Test all components"""
    print("🧪 Testing All Wasfaty Analysis Components")
    print("=" * 60)
    print(f"Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Activate virtual environment prefix
    venv_prefix = "source venv/bin/activate && "
    
    tests = [
        {
            'command': f'{venv_prefix}python run_geospatial_analysis.py',
            'description': 'Geospatial Analysis Pipeline',
            'critical': True
        },
        {
            'command': f'{venv_prefix}python temporal_analysis_simple.py',
            'description': 'Temporal Analysis Pipeline',
            'critical': True
        },
        {
            'command': f'{venv_prefix}jupyter nbconvert --to notebook --execute advanced_geospatial_analysis.ipynb --output test_geospatial.ipynb',
            'description': 'Geospatial Jupyter Notebook',
            'critical': False
        }
    ]
    
    # Check if required files exist
    required_files = [
        'daily_logs/merged_logs.csv',
        'data_utils.py',
        'geospatial_utils.py',
        'requirements.txt'
    ]
    
    print(f"\n📁 Checking required files...")
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - MISSING")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n❌ Missing required files: {missing_files}")
        return False
    
    # Run tests
    results = []
    critical_failures = 0
    
    for test in tests:
        success = run_command(test['command'], test['description'])
        results.append({
            'test': test['description'],
            'success': success,
            'critical': test['critical']
        })
        
        if not success and test['critical']:
            critical_failures += 1
    
    # Summary
    print(f"\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(1 for r in results if r['success'])
    
    for result in results:
        status = "✅ PASS" if result['success'] else "❌ FAIL"
        critical = " (CRITICAL)" if result['critical'] else ""
        print(f"{status} - {result['test']}{critical}")
    
    print(f"\nOverall: {passed_tests}/{total_tests} tests passed")
    
    if critical_failures == 0:
        print(f"\n🎉 ALL CRITICAL TESTS PASSED!")
        print(f"✅ System is ready for production use")
        
        # Check generated files
        generated_files = [
            'supply_demand_analysis.html',
            'analysis_summary.json',
            'temporal_analysis_summary.json'
        ]
        
        print(f"\n📄 Generated Files:")
        for file in generated_files:
            if os.path.exists(file):
                size = os.path.getsize(file)
                print(f"✅ {file} ({size:,} bytes)")
            else:
                print(f"⚠️ {file} - Not found")
        
        return True
    else:
        print(f"\n❌ {critical_failures} CRITICAL TEST(S) FAILED!")
        print(f"⚠️ System requires fixes before production use")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
