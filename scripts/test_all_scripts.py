#!/usr/bin/env python3
"""
Test script to verify all analysis scripts work correctly
"""

import os
import sys
import subprocess

# Add project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def test_script(script_name):
    """Test a single script"""
    script_path = os.path.join(project_root, 'scripts', script_name)
    
    print(f"\n🧪 Testing {script_name}...")
    print("-" * 50)
    
    try:
        # Run the script
        result = subprocess.run([sys.executable, script_path], 
                              capture_output=True, 
                              text=True, 
                              timeout=120)
        
        if result.returncode == 0:
            print(f"✅ {script_name} executed successfully!")
            return True
        else:
            print(f"❌ {script_name} failed with return code {result.returncode}")
            print(f"Error output: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {script_name} timed out after 120 seconds")
        return False
    except Exception as e:
        print(f"❌ {script_name} failed with exception: {str(e)}")
        return False

def test_shell_script(script_name):
    """Test a shell script"""
    script_path = os.path.join(project_root, 'scripts', script_name)

    print(f"\n🧪 Testing {script_name}...")
    print("-" * 50)

    try:
        # Run the shell script
        result = subprocess.run(['bash', script_path],
                              capture_output=True,
                              text=True,
                              timeout=120,
                              cwd=project_root)

        if result.returncode == 0:
            print(f"✅ {script_name} executed successfully!")
            return True
        else:
            print(f"❌ {script_name} failed with return code {result.returncode}")
            print(f"Error output: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        print(f"⏰ {script_name} timed out after 120 seconds")
        return False
    except Exception as e:
        print(f"❌ {script_name} failed with exception: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Wasfaty Analytics Test Suite")
    print("=" * 60)
    
    scripts_to_test = [
        'run_geospatial_analysis.py',
        'temporal_analysis_simple.py'
    ]

    shell_scripts_to_test = [
        'export_kml.sh'
    ]
    
    results = {}

    # Test Python scripts
    for script in scripts_to_test:
        results[script] = test_script(script)

    # Test shell scripts
    for script in shell_scripts_to_test:
        results[script] = test_shell_script(script)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = sum(results.values())
    total = len(results)
    
    for script, passed_test in results.items():
        status = "✅ PASSED" if passed_test else "❌ FAILED"
        print(f"• {script}: {status}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The codebase is ready for use.")
        return True
    else:
        print("⚠️ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
