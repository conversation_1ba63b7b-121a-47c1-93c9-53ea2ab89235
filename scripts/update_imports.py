#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to update import statements in notebooks to work with new directory structure.
"""

import os
import re
import json
from pathlib import Path

def update_notebook_imports(notebook_path):
    """Update import statements in a Jupyter notebook."""
    print(f"Updating imports in {notebook_path}")
    
    with open(notebook_path, 'r', encoding='utf-8') as f:
        notebook = json.load(f)
    
    updated = False
    
    for cell in notebook.get('cells', []):
        if cell.get('cell_type') == 'code':
            source = cell.get('source', [])
            new_source = []
            
            for line in source:
                # Update import statements
                if 'from data_utils import' in line:
                    new_line = line.replace('from data_utils import', 'from src.data_utils import')
                    new_source.append(new_line)
                    updated = True
                elif 'from geospatial_utils import' in line:
                    new_line = line.replace('from geospatial_utils import', 'from src.geospatial_utils import')
                    new_source.append(new_line)
                    updated = True
                elif 'import data_utils' in line:
                    new_line = line.replace('import data_utils', 'import src.data_utils as data_utils')
                    new_source.append(new_line)
                    updated = True
                elif 'import geospatial_utils' in line:
                    new_line = line.replace('import geospatial_utils', 'import src.geospatial_utils as geospatial_utils')
                    new_source.append(new_line)
                    updated = True
                else:
                    new_source.append(line)
            
            cell['source'] = new_source
    
    if updated:
        # Backup original file
        backup_path = str(notebook_path) + '.backup'
        os.rename(str(notebook_path), backup_path)
        
        # Write updated notebook
        with open(str(notebook_path), 'w', encoding='utf-8') as f:
            json.dump(notebook, f, indent=2)
        
        print(f"  ✅ Updated {notebook_path}")
        return True
    else:
        print(f"  ℹ️  No updates needed for {notebook_path}")
        return False

def update_python_scripts(script_path):
    """Update import statements in Python scripts."""
    print(f"Updating imports in {script_path}")
    
    with open(script_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # Update import statements
    content = re.sub(r'from data_utils import', 'from src.data_utils import', content)
    content = re.sub(r'from geospatial_utils import', 'from src.geospatial_utils import', content)
    content = re.sub(r'import data_utils(?!\w)', 'import src.data_utils as data_utils', content)
    content = re.sub(r'import geospatial_utils(?!\w)', 'import src.geospatial_utils as geospatial_utils', content)
    
    if content != original_content:
        # Backup original file
        backup_path = str(script_path) + '.backup'
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(original_content)

        # Write updated script
        with open(str(script_path), 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"  ✅ Updated {script_path}")
        return True
    else:
        print(f"  ℹ️  No updates needed for {script_path}")
        return False

def main():
    """Main function to update all files."""
    project_root = Path(__file__).parent.parent
    
    print("🔄 Updating import statements in notebooks and scripts...")
    print(f"Project root: {project_root}")
    
    updated_files = []
    
    # Update notebooks
    notebooks_dir = project_root / 'notebooks'
    if notebooks_dir.exists():
        for notebook_path in notebooks_dir.rglob('*.ipynb'):
            if update_notebook_imports(notebook_path):
                updated_files.append(str(notebook_path))
    
    # Update Python scripts
    scripts_dir = project_root / 'scripts'
    if scripts_dir.exists():
        for script_path in scripts_dir.glob('*.py'):
            if script_path.name != 'update_imports.py':  # Skip this script
                if update_python_scripts(script_path):
                    updated_files.append(str(script_path))
    
    print(f"\n✅ Import update complete!")
    print(f"Updated {len(updated_files)} files:")
    for file_path in updated_files:
        print(f"  - {file_path}")
    
    if updated_files:
        print(f"\n📁 Backup files created with .backup extension")
        print(f"💡 Test your notebooks and remove backup files when satisfied")

if __name__ == '__main__':
    main()
