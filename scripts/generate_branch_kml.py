#!/usr/bin/env python3
"""
Generate Enhanced KML files from Wasfaty pharmacy branch data

This script generates two detailed KML files for visualizing pharmacy branch tiers
and top branches by order volume, with enhanced visualization options and rich data
for immediate import into Google Maps or Google Earth.

Usage:
    python generate_branch_kml.py

Requirements:
    - Python 3.6+
    - simplekml
    - pandas
    - numpy (for data processing)
"""

import os
import math
import pandas as pd
import numpy as np
import simplekml
from datetime import datetime

def load_branch_data(input_file='branch_data.csv'):
    """Load branch data from CSV file"""
    df = pd.read_csv(input_file)
    if 'branch_license' not in df.columns:
        raise ValueError("CSV must contain 'branch_license' column")
    if 'latitude' not in df.columns or 'longitude' not in df.columns:
        raise ValueError("CSV must contain 'latitude' and 'longitude' columns")
    if 'order_id' not in df.columns and 'order_count' not in df.columns:
        print("Warning: No order count column found, using default values")
        df['order_id'] = 1
    return df

def assign_tiers(df, store_lat, store_lon):
    """Assign branches to tiers based on distance from dark store"""
    # Calculate distance to the dark store
    df['distance_km'] = df.apply(
        lambda row: haversine(store_lat, store_lon, row['latitude'], row['longitude']), axis=1
    )

    # Define tier boundaries
    tier1_max = 5   # km
    # Tier 1: within 5km
    # Tier 2: 5-15km
    # Tier 3: >15km
    df['tier'] = df['distance_km'].apply(
        lambda dist: 'Tier 1' if dist <= tier1_max else ('Tier 2' if dist <= 15 else 'Tier 3')
    )

    # Identify top 30 branches by order volume
    order_col = 'order_id' if 'order_id' in df.columns else 'order_count'
    df['is_top30'] = df[order_col].rank(method='first', ascending=False) <= 30
    
    return df

def haversine(lat1, lon1, lat2, lon2):
    """Calculate the great-circle distance between two points on the Earth"""
    R = 6371  # Earth radius in kilometers
    phi1, phi2 = math.radians(lat1), math.radians(lat2)
    dphi = math.radians(lat2 - lat1)
    dlambda = math.radians(lon2 - lon1)
    a = math.sin(dphi/2)**2 + math.cos(phi1)*math.cos(phi2)*math.sin(dlambda/2)**2
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
    return R * c

def create_circle_polygon(kml_doc, center_lat, center_lon, radius_m, name, color, line_width=2, fill_opacity=50):
    """Create a circle as a polygon in KML"""
    # Create polygon
    pol = kml_doc.newpolygon(name=name)
    
    # Generate circle points (36 points for a full circle)
    coords = []
    for angle in range(0, 360, 10):  # 10-degree steps
        # Convert angle to radians
        rad = math.radians(angle)
        
        # Calculate lat/lon for this point on the circle
        # Use haversine formula but backwards (from distance to lat/lon)
        lat_rad = math.asin(math.sin(math.radians(center_lat)) * math.cos(radius_m/6371000) + 
                           math.cos(math.radians(center_lat)) * math.sin(radius_m/6371000) * math.cos(rad))
        
        lon_rad = math.radians(center_lon) + math.atan2(math.sin(rad) * math.sin(radius_m/6371000) * math.cos(math.radians(center_lat)),
                                                     math.cos(radius_m/6371000) - math.sin(math.radians(center_lat)) * math.sin(lat_rad))
        
        # Convert back to degrees
        lat = math.degrees(lat_rad)
        lon = math.degrees(lon_rad)
        
        # Add to coordinates (KML uses lon, lat)
        coords.append((lon, lat, 0))
    
    # Close the polygon
    coords.append(coords[0])
    
    # Set coordinates and styles
    pol.outerboundaryis = coords
    pol.style.linestyle.color = color
    pol.style.linestyle.width = line_width
    pol.style.polystyle.color = simplekml.Color.changealphaint(fill_opacity, color)
    
    return pol

def generate_kml_files(branch_df, store_lat, store_lon, output_dir='.'):
    """Generate enhanced KML files for branches by tier and top branches with detailed visualization"""
    print("Exporting enhanced branch tier data to KML files for Google Maps...")

    # Create a new KML document for all branches by tier
    kml_all_branches = simplekml.Kml()
    kml_all_branches.document.name = "Wasfaty Pharmacy Branches by Tier"
    
    # Calculate statistics for rich insights
    order_col = 'order_id' if 'order_id' in branch_df.columns else 'order_count'
    total_branches = len(branch_df)
    tier1_count = len(branch_df[branch_df['tier'] == 'Tier 1'])
    tier2_count = len(branch_df[branch_df['tier'] == 'Tier 2'])
    tier3_count = len(branch_df[branch_df['tier'] == 'Tier 3'])
    tier1_pct = tier1_count / total_branches * 100
    tier2_pct = tier2_count / total_branches * 100
    tier3_pct = tier3_count / total_branches * 100
    
    # Calculate order volume by tier if possible
    if order_col in branch_df.columns:
        total_orders = branch_df[order_col].sum()
        tier1_orders = branch_df[branch_df['tier'] == 'Tier 1'][order_col].sum()
        tier2_orders = branch_df[branch_df['tier'] == 'Tier 2'][order_col].sum()
        tier3_orders = branch_df[branch_df['tier'] == 'Tier 3'][order_col].sum()
        tier1_order_pct = tier1_orders / total_orders * 100 if total_orders > 0 else 0
        tier2_order_pct = tier2_orders / total_orders * 100 if total_orders > 0 else 0
        tier3_order_pct = tier3_orders / total_orders * 100 if total_orders > 0 else 0
        top30_orders = branch_df[branch_df['is_top30']][order_col].sum()
        top30_pct = top30_orders / total_orders * 100 if total_orders > 0 else 0
    else:
        total_orders = 0
        tier1_orders = tier2_orders = tier3_orders = 0
        tier1_order_pct = tier2_order_pct = tier3_order_pct = 0
        top30_orders = 0
        top30_pct = 0
    
    kml_all_branches.document.description = f"""
    <div style="font-family: Arial, sans-serif; max-width: 800px; padding: 15px; background-color: #f9f9f9; border-radius: 10px; border: 1px solid #ddd;">
        <h1 style="color: #2c3e50; text-align: center; border-bottom: 3px solid #3498db; padding-bottom: 10px;">
            Wasfaty Pharmacy Branch Network Analysis
        </h1>
        
        <div style="background-color: #e8f4f8; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 5px solid #3498db;">
            <p><b style="color: #2980b9;">Generated:</b> {datetime.now().strftime('%B %d, %Y at %H:%M')}</p>
            <p><b style="color: #2980b9;">Total Branches:</b> {total_branches}</p>
            <p><b style="color: #2980b9;">Total Order Volume:</b> {total_orders:,}</p>
        </div>
        
        <h2 style="color: #2c3e50; margin-top: 20px;">Branch Distribution by Tier</h2>
        
        <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
            <thead>
                <tr style="background-color: #3498db; color: white;">
                    <th style="padding: 10px; text-align: left;">Tier</th>
                    <th style="padding: 10px; text-align: right;">Branches</th>
                    <th style="padding: 10px; text-align: right;">% of Network</th>
                    <th style="padding: 10px; text-align: right;">Order Volume</th>
                    <th style="padding: 10px; text-align: right;">% of Orders</th>
                </tr>
            </thead>
            <tbody>
                <tr style="background-color: #e8f8e8;">
                    <td style="padding: 8px; border: 1px solid #ddd;"><b>Tier 1 (Green)</b><br>≤5km from dark store</td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">{tier1_count}</td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">{tier1_pct:.1f}%</td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">{tier1_orders:,}</td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">{tier1_order_pct:.1f}%</td>
                </tr>
                <tr style="background-color: #e8f0f8;">
                    <td style="padding: 8px; border: 1px solid #ddd;"><b>Tier 2 (Blue)</b><br>5-15km from dark store</td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">{tier2_count}</td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">{tier2_pct:.1f}%</td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">{tier2_orders:,}</td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">{tier2_order_pct:.1f}%</td>
                </tr>
                <tr style="background-color: #f8e8e8;">
                    <td style="padding: 8px; border: 1px solid #ddd;"><b>Tier 3 (Red)</b><br>>15km from dark store</td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">{tier3_count}</td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">{tier3_pct:.1f}%</td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">{tier3_orders:,}</td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">{tier3_order_pct:.1f}%</td>
                </tr>
                <tr style="background-color: #f2e5f9; font-weight: bold;">
                    <td style="padding: 8px; border: 1px solid #ddd;"><b>Top 30 Branches</b><br>(Purple Stars)</td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">30</td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">{30/total_branches*100:.1f}%</td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">{top30_orders:,}</td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">{top30_pct:.1f}%</td>
                </tr>
            </tbody>
        </table>

        <div style="background-color: #FDEBD0; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 5px solid #F39C12;">
            <h3 style="color: #E67E22; margin-top: 0;">Key Operational Insights</h3>
            <ul style="list-style-type: square;">
                <li>The size of each icon correlates with order volume - larger icons represent higher volume branches.</li>
                <li>Top 30 branches ({30/total_branches*100:.1f}% of network) represent {top30_pct:.1f}% of total order volume.</li>
                <li>Tier 3 branches ({tier3_pct:.1f}% of network) generate {tier3_order_pct:.1f}% of order volume and may require special logistics planning.</li>
            </ul>
        </div>
        
        <div style="font-size: 12px; color: #7f8c8d; text-align: right; margin-top: 20px; font-style: italic;">
            Click on any branch marker for detailed operational insights and recommendations.
        </div>
    </div>
    """

    # Create a separate KML for top 30 branches
    kml_top_branches = simplekml.Kml()
    kml_top_branches.document.name = "Wasfaty Top 30 High-Volume Pharmacy Branches"
    
    # Calculate top 30 stats by tier
    top30_branches = branch_df[branch_df['is_top30']]
    top30_tier1 = len(top30_branches[top30_branches['tier'] == 'Tier 1'])
    top30_tier2 = len(top30_branches[top30_branches['tier'] == 'Tier 2'])
    top30_tier3 = len(top30_branches[top30_branches['tier'] == 'Tier 3'])
    
    # Calculate average distance for top 30 branches
    avg_distance = top30_branches['distance_km'].mean()
    
    kml_top_branches.document.description = f"""
    <div style="font-family: Arial, sans-serif; max-width: 800px; padding: 15px; background-color: #f9f9f9; border-radius: 10px; border: 1px solid #ddd;">
        <h1 style="color: #2c3e50; text-align: center; border-bottom: 3px solid #8e44ad; padding-bottom: 10px;">
            Wasfaty Top 30 High-Volume Pharmacy Branches
        </h1>
        
        <div style="background-color: #f2e5f9; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 5px solid #8e44ad;">
            <p><b style="color: #8e44ad;">Generated:</b> {datetime.now().strftime('%B %d, %Y at %H:%M')}</p>
            <p><b style="color: #8e44ad;">Order Volume Impact:</b> These top 30 branches ({30/len(branch_df)*100:.1f}% of network) represent {top30_pct:.1f}% of total order volume.</p>
            <p><b style="color: #8e44ad;">Average Distance from Dark Store:</b> {avg_distance:.2f} km</p>
        </div>
        
        <h2 style="color: #2c3e50; margin-top: 20px;">Operational Priority Analysis</h2>
        
        <div style="display: flex; justify-content: space-between; text-align: center; margin: 20px 0;">
            <div style="background-color: #e8f8e8; padding: 15px; border-radius: 8px; flex: 1; margin-right: 10px;">
                <h3 style="color: #27ae60; margin-top: 0;">Tier 1 Branches</h3>
                <div style="font-size: 24px; font-weight: bold;">{top30_tier1}</div>
                <p>≤5km from dark store</p>
            </div>
            <div style="background-color: #e8f0f8; padding: 15px; border-radius: 8px; flex: 1; margin-right: 10px;">
                <h3 style="color: #2980b9; margin-top: 0;">Tier 2 Branches</h3>
                <div style="font-size: 24px; font-weight: bold;">{top30_tier2}</div>
                <p>5-15km from dark store</p>
            </div>
            <div style="background-color: #f8e8e8; padding: 15px; border-radius: 8px; flex: 1;">
                <h3 style="color: #c0392b; margin-top: 0;">Tier 3 Branches</h3>
                <div style="font-size: 24px; font-weight: bold;">{top30_tier3}</div>
                <p>>15km from dark store</p>
            </div>
        </div>
        
        <div style="background-color: #FDEBD0; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 5px solid #F39C12;">
            <h3 style="color: #E67E22; margin-top: 0;">Key Operational Recommendations</h3>
            <ul style="list-style-type: square;">
                <li>Prioritize these branches for operational excellence and resource allocation</li>
                <li>Implement enhanced order tracking and fulfillment accuracy checks</li>
                <li>For Tier 3 high-volume branches ({top30_tier3} branches), develop specialized logistics plans</li>
                <li>Consider satellite distribution points if multiple high-volume branches exist in distant areas</li>
            </ul>
        </div>
        
        <div style="font-size: 12px; color: #7f8c8d; text-align: right; margin-top: 20px; font-style: italic;">
            Refer to the detailed data table in the left sidebar for branch-specific metrics and recommendations.
        </div>
    </div>
    """

    # Create folders for each tier in the all branches KML with detailed descriptions
    folder_tier1 = kml_all_branches.newfolder(name="Tier 1 Branches (≤5km)")
    folder_tier1.description = """
    <h2>Tier 1 Branches (≤5km)</h2>
    <p>These branches are within 5km of the dark store and are prime candidates for:</p>
    <ul>
        <li>Rapid delivery services (30 min or less)</li>
        <li>High-frequency restocking</li>
        <li>Testing new products and services</li>
    </ul>
    """
    
    folder_tier2 = kml_all_branches.newfolder(name="Tier 2 Branches (5-15km)")
    folder_tier2.description = """
    <h2>Tier 2 Branches (5-15km)</h2>
    <p>These branches are between 5-15km from the dark store and should be considered for:</p>
    <ul>
        <li>Standard delivery services (1-2 hour window)</li>
        <li>Regular restocking schedules</li>
        <li>Focused service improvements</li>
    </ul>
    """
    
    folder_tier3 = kml_all_branches.newfolder(name="Tier 3 Branches (>15km)")
    folder_tier3.description = """
    <h2>Tier 3 Branches (>15km)</h2>
    <p>These branches are beyond 15km from the dark store and may require:</p>
    <ul>
        <li>Extended delivery windows</li>
        <li>Special logistics arrangements</li>
        <li>Potential satellite operation points</li>
        <li>Alternative fulfillment strategies</li>
    </ul>
    <p>High-volume Tier 3 branches may indicate areas where a new dark store would be beneficial.</p>
    """

    # Calculate order volume statistics for scaling markers
    if 'order_id' in branch_df.columns:
        order_col = 'order_id'
    elif 'order_count' in branch_df.columns:
        order_col = 'order_count'
    else:
        order_col = None
    
    if order_col:
        min_orders = branch_df[order_col].min()
        max_orders = branch_df[order_col].max()
        order_range = max_orders - min_orders
    
    # Create style maps for different tiers and volume levels
    def create_tiered_styles(base_color, icon, prefix="tier"):
        """Create a set of styles for different order volumes within a tier"""
        styles = {}
        
        # Create 5 levels of size based on order volume
        for i in range(1, 6):
            size_scale = 0.7 + (i * 0.2)  # Scale from 0.9 to 1.7
            
            # Normal style (when not highlighted)
            normal_style = simplekml.Style()
            normal_style.iconstyle.icon.href = icon
            normal_style.iconstyle.color = base_color
            normal_style.iconstyle.scale = size_scale
            normal_style.labelstyle.color = base_color
            normal_style.labelstyle.scale = 0.8
            normal_style.balloonstyle.bgcolor = simplekml.Color.lightblue
            normal_style.balloonstyle.textcolor = simplekml.Color.black
            
            # Highlighted style (when clicked)
            highlight_style = simplekml.Style()
            highlight_style.iconstyle.icon.href = icon
            highlight_style.iconstyle.color = base_color
            highlight_style.iconstyle.scale = size_scale * 1.3  # Bigger when highlighted
            highlight_style.labelstyle.color = base_color
            highlight_style.labelstyle.scale = 1.0  # Bigger text when highlighted
            highlight_style.balloonstyle.bgcolor = simplekml.Color.white
            highlight_style.balloonstyle.textcolor = simplekml.Color.black
            
            # Create a style map combining normal and highlighted states
            style_map = simplekml.StyleMap()
            style_map.normalstyle = normal_style
            style_map.highlightstyle = highlight_style
            
            style_key = f"{prefix}_{i}"
            styles[style_key] = style_map
            
        return styles
    
    # Create tier-based style maps for different order volumes
    # Enhanced colors with better visibility
    tier1_color = simplekml.Color.changealphaint(200, simplekml.Color.green)
    tier2_color = simplekml.Color.changealphaint(200, simplekml.Color.blue)
    tier3_color = simplekml.Color.changealphaint(200, simplekml.Color.red)
    
    # Create style maps for each tier
    tier1_styles = create_tiered_styles(
        tier1_color, 
        'http://maps.google.com/mapfiles/kml/paddle/grn-circle.png', 
        "tier1"
    )
    
    tier2_styles = create_tiered_styles(
        tier2_color, 
        'http://maps.google.com/mapfiles/kml/paddle/blu-circle.png', 
        "tier2"
    )
    
    tier3_styles = create_tiered_styles(
        tier3_color, 
        'http://maps.google.com/mapfiles/kml/paddle/red-circle.png', 
        "tier3"
    )
    
    # Style maps for top 30 branches
    top30_styles = create_tiered_styles(
        simplekml.Color.changealphaint(200, simplekml.Color.purple),
        'http://maps.google.com/mapfiles/kml/shapes/star.png',
        "top30"
    )
    
    # Style for the dark store
    style_dark_store = simplekml.Style()
    style_dark_store.iconstyle.icon.href = 'http://maps.google.com/mapfiles/kml/shapes/shopping.png'
    style_dark_store.iconstyle.color = simplekml.Color.black  # Black
    style_dark_store.iconstyle.scale = 1.5
    style_dark_store.labelstyle.color = simplekml.Color.black
    style_dark_store.labelstyle.scale = 1.2
    style_dark_store.balloonstyle.bgcolor = simplekml.Color.white
    style_dark_store.balloonstyle.textcolor = simplekml.Color.black

    # Add dark store folder to both KML files
    dark_store_folder1 = kml_all_branches.newfolder(name="Dark Store Location")
    dark_store_folder1.description = """
    <h2>Central Dark Store Location</h2>
    <p>This is the central warehouse and distribution center for pharmacy inventory.</p>
    <p>The tier boundaries are measured from this location.</p>
    """
    
    dark_store_folder2 = kml_top_branches.newfolder(name="Dark Store Location")
    dark_store_folder2.description = dark_store_folder1.description
    
    # Create points for dark stores - corrected method calls
    dark_store_pt1 = dark_store_folder1.newpoint(name="Current Dark Store")
    dark_store_pt1.coords = [(store_lon, store_lat, 0)]  # KML uses (lon, lat, alt) format
    dark_store_pt1.style = style_dark_store
    dark_store_pt1.description = f"""
    <div style="font-family: Arial, sans-serif; max-width: 550px;">
        <div style="background-color: #2c3e50; color: white; padding: 15px; text-align: center; border-radius: 8px 8px 0 0;">
            <h2 style="margin: 0;">Central Dark Store Facility</h2>
            <p style="margin: 5px 0 0 0; opacity: 0.8;">Primary Distribution Hub</p>
        </div>
        
        <div style="border: 1px solid #2c3e50; border-top: none; padding: 20px; border-radius: 0 0 8px 8px;">
            <div style="display: flex; margin-bottom: 15px; border-bottom: 1px solid #eee; padding-bottom: 15px;">
                <div style="width: 40px; text-align: center; margin-right: 15px; font-size: 24px;">📍</div>
                <div>
                    <div style="color: #7f8c8d; font-size: 14px;">Location Coordinates</div>
                    <div style="font-weight: bold;">{store_lat:.6f}, {store_lon:.6f}</div>
                </div>
            </div>
            
            <div style="display: flex; margin-bottom: 15px; border-bottom: 1px solid #eee; padding-bottom: 15px;">
                <div style="width: 40px; text-align: center; margin-right: 15px; font-size: 24px;">🏢</div>
                <div>
                    <div style="color: #7f8c8d; font-size: 14px;">Facility Address</div>
                    <div style="font-weight: bold;">[Insert actual address here]</div>
                </div>
            </div>
            
            <div style="display: flex; margin-bottom: 15px; border-bottom: 1px solid #eee; padding-bottom: 15px;">
                <div style="width: 40px; text-align: center; margin-right: 15px; font-size: 24px;">⏰</div>
                <div>
                    <div style="color: #7f8c8d; font-size: 14px;">Operations Hours</div>
                    <div style="font-weight: bold;">24/7 Operation</div>
                </div>
            </div>
            
            <div>
                <h3 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 8px;">Network Coverage</h3>
                
                <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                    <tr style="background-color: #f9f9f9;">
                        <th style="padding: 8px; text-align: left; border: 1px solid #ddd;">Service Zone</th>
                        <th style="padding: 8px; text-align: right; border: 1px solid #ddd;">Branches</th>
                        <th style="padding: 8px; text-align: left; border: 1px solid #ddd;">Delivery Window</th>
                    </tr>
                    <tr>
                        <td style="padding: 8px; border: 1px solid #ddd; color: #27ae60;"><b>Tier 1 (≤5km)</b></td>
                        <td style="padding: 8px; border: 1px solid #ddd; text-align: right; font-weight: bold;">{len(branch_df[branch_df['tier'] == 'Tier 1'])}</td>
                        <td style="padding: 8px; border: 1px solid #ddd;">30 min or less</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; border: 1px solid #ddd; color: #2980b9;"><b>Tier 2 (5-15km)</b></td>
                        <td style="padding: 8px; border: 1px solid #ddd; text-align: right; font-weight: bold;">{len(branch_df[branch_df['tier'] == 'Tier 2'])}</td>
                        <td style="padding: 8px; border: 1px solid #ddd;">1-2 hours</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; border: 1px solid #ddd; color: #c0392b;"><b>Tier 3 (>15km)</b></td>
                        <td style="padding: 8px; border: 1px solid #ddd; text-align: right; font-weight: bold;">{len(branch_df[branch_df['tier'] == 'Tier 3'])}</td>
                        <td style="padding: 8px; border: 1px solid #ddd;">2+ hours</td>
                    </tr>
                    <tr style="background-color: #f9f9f9;">
                        <td style="padding: 8px; border: 1px solid #ddd;"><b>Total Network</b></td>
                        <td style="padding: 8px; border: 1px solid #ddd; text-align: right; font-weight: bold;">{len(branch_df)}</td>
                        <td style="padding: 8px; border: 1px solid #ddd;">-</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    """

    # Create dark store point for top branches KML
    dark_store_pt2 = dark_store_folder2.newpoint(name="Current Dark Store")
    dark_store_pt2.coords = [(store_lon, store_lat, 0)]  # KML uses (lon, lat, alt) format
    dark_store_pt2.style = style_dark_store
    dark_store_pt2.description = dark_store_pt1.description

    # Create boundary folder in each KML
    boundary_folder1 = kml_all_branches.newfolder(name="Service Zone Boundaries")
    boundary_folder1.description = """
    <h2>Service Zone Boundaries</h2>
    <p>These boundaries define the service tiers based on distance from the dark store:</p>
    <ul>
        <li><b>Tier 1 (Green):</b> ≤5km radius - Priority service area</li>
        <li><b>Tier 2 (Blue):</b> 5-15km radius - Standard service area</li>
    </ul>
    <p>Branches beyond 15km are classified as Tier 3 and may require special logistics arrangements.</p>
    """
    
    boundary_folder2 = kml_top_branches.newfolder(name="Service Zone Boundaries")
    boundary_folder2.description = boundary_folder1.description

    # Add tier boundary circles to both KML files
    # Tier 1 circle (5km) - Enhanced version with better colors
    tier1_circle1 = create_circle_polygon(
        boundary_folder1, 
        center_lat=store_lat, 
        center_lon=store_lon, 
        radius_m=5000, 
        name="Tier 1 Boundary (≤5km)",
        color=simplekml.Color.changealphaint(120, simplekml.Color.green), 
        fill_opacity=25,
        line_width=3
    )
    tier1_circle1.description = """
    <h3>Tier 1 Service Zone (≤5km)</h3>
    <p>This zone represents the area within 5km of the dark store, suitable for:</p>
    <ul>
        <li>Rapid delivery services (30 minutes or less)</li>
        <li>Multiple daily deliveries</li>
        <li>Emergency or urgent service</li>
    </ul>
    <p><b>Number of branches:</b> """ + str(len(branch_df[branch_df['tier'] == 'Tier 1'])) + """</p>
    """

    tier1_circle2 = create_circle_polygon(
        boundary_folder2, 
        center_lat=store_lat, 
        center_lon=store_lon, 
        radius_m=5000, 
        name="Tier 1 Boundary (≤5km)",
        color=simplekml.Color.changealphaint(120, simplekml.Color.green), 
        fill_opacity=25,
        line_width=3
    )
    tier1_circle2.description = tier1_circle1.description

    # Tier 2 circle (15km) - Enhanced version
    tier2_circle1 = create_circle_polygon(
        boundary_folder1, 
        center_lat=store_lat, 
        center_lon=store_lon, 
        radius_m=15000, 
        name="Tier 2 Boundary (≤15km)",
        color=simplekml.Color.changealphaint(120, simplekml.Color.blue), 
        fill_opacity=15,
        line_width=3
    )
    tier2_circle1.description = """
    <h3>Tier 2 Service Zone (5-15km)</h3>
    <p>This zone represents the area between 5km and 15km from the dark store, suitable for:</p>
    <ul>
        <li>Standard delivery service (1-2 hour window)</li>
        <li>Daily scheduled deliveries</li>
        <li>Standard service operations</li>
    </ul>
    <p><b>Number of branches:</b> """ + str(len(branch_df[branch_df['tier'] == 'Tier 2'])) + """</p>
    """

    tier2_circle2 = create_circle_polygon(
        boundary_folder2, 
        center_lat=store_lat, 
        center_lon=store_lon, 
        radius_m=15000, 
        name="Tier 2 Boundary (≤15km)",
        color=simplekml.Color.changealphaint(120, simplekml.Color.blue), 
        fill_opacity=15,
        line_width=3
    )
    tier2_circle2.description = tier2_circle1.description

    # Count for statistics
    added_by_tier = {"Tier 1": 0, "Tier 2": 0, "Tier 3": 0}
    top30_by_tier = {"Tier 1": 0, "Tier 2": 0, "Tier 3": 0}
    
    # Get order volume range for scaling
    if order_col:
        min_orders = branch_df[order_col].min()
        max_orders = branch_df[order_col].max()
        order_range = max_orders - min_orders if max_orders > min_orders else 1

    # Add all branches to the KML files based on their tier and top status
    for _, row in branch_df.iterrows():
        # Format description with branch details
        area = row.get('area', 'N/A')
        order_col = 'order_id' if 'order_id' in row.index else 'order_count' if 'order_count' in row.index else None
        order_count = row.get(order_col, 'N/A') if order_col else 'N/A'
        
        # Calculate what size category this branch belongs to (1-5)
        if order_col and isinstance(order_count, (int, float)):
            # Calculate size category (1-5) based on order volume percentile
            size_category = 1
            if order_range > 0:
                percentile = (order_count - min_orders) / order_range
                size_category = min(5, max(1, int(percentile * 5) + 1))
        else:
            size_category = 1
        
        # Calculate order volume percentile rank if possible
        percentile_rank = None
        if order_col and isinstance(order_count, (int, float)) and order_count > 0:
            if hasattr(branch_df, 'rank'):
                percentile_rank = branch_df[order_col].rank(pct=True).loc[row.name] * 100
        
        # Set tier-specific styling
        if row['tier'] == 'Tier 1':
            tier_color = "#27ae60"  # Green
            tier_bg = "#e8f8e8"
            tier_icon = "🟢"
            delivery_time = "30 min or less"
        elif row['tier'] == 'Tier 2':
            tier_color = "#2980b9"  # Blue
            tier_bg = "#e8f0f8"
            tier_icon = "🔵"
            delivery_time = "1-2 hours"
        else:  # Tier 3
            tier_color = "#c0392b"  # Red
            tier_bg = "#f8e8e8"
            tier_icon = "🔴" 
            delivery_time = "2+ hours"

        # Set volume-specific information
        if row['is_top30']:
            volume_badge = "<span style='background-color: #8e44ad; color: white; padding: 3px 8px; border-radius: 12px; font-size: 12px;'>TOP 30</span>"
            priority = "Critical Priority"
            priority_color = "#8e44ad"  # Purple
            star_icon = "⭐"
        else:
            volume_badge = ""
            if size_category >= 4:
                priority = "High Priority"
                priority_color = "#e67e22"  # Orange
                star_icon = "✨"
            elif size_category >= 2:
                priority = "Medium Priority"
                priority_color = "#f1c40f"  # Yellow
                star_icon = "✓"
            else:
                priority = "Standard Priority"
                priority_color = "#7f8c8d"  # Gray
                star_icon = "•"

        # Create rich HTML description with detailed branch information
        desc = f"""
        <div style="font-family: Arial, sans-serif; max-width: 550px;">
            <div style="display: flex; align-items: center; border-bottom: 2px solid {tier_color}; padding-bottom: 10px; margin-bottom: 15px;">
                <div style="font-size: 24px; margin-right: 10px;">{tier_icon}</div>
                <div>
                    <h2 style="color: #2c3e50; margin: 0;">
                        Branch: {row['branch_license']} {volume_badge}
                    </h2>
                    <p style="margin: 5px 0 0 0; color: #7f8c8d;">{area}</p>
                </div>
            </div>
            
            <div style="display: flex; margin-bottom: 20px;">
                <div style="flex: 1; background-color: {tier_bg}; padding: 15px; border-radius: 8px; margin-right: 10px;">
                    <h3 style="color: {tier_color}; margin-top: 0; text-align: center;">Location</h3>
                    <div style="text-align: center; font-size: 24px; font-weight: bold;">{row['tier']}</div>
                    <p style="text-align: center; margin-bottom: 0;">{row['distance_km']:.1f} km from dark store</p>
                </div>
                
                <div style="flex: 1; background-color: #fef9e7; padding: 15px; border-radius: 8px;">
                    <h3 style="color: {priority_color}; margin-top: 0; text-align: center;">Volume</h3>
                    <div style="text-align: center; font-size: 24px; font-weight: bold; display: flex; justify-content: center; align-items: center;">
                        <span style="margin-right: 5px;">{star_icon}</span>{order_count}
                    </div>
                    <p style="text-align: center; margin-bottom: 0;">
                        {f"Top {percentile_rank:.0f}%" if percentile_rank is not None else f"Category {size_category}/5"}
                    </p>
                </div>
            </div>
            
            <div style="background-color: #f8f9fa; border-left: 5px solid {priority_color}; padding: 15px; margin-bottom: 20px;">
                <h3 style="color: #2c3e50; margin-top: 0;">Operational Details</h3>
                <table style="width: 100%;">
                    <tr>
                        <td style="padding: 5px; color: #7f8c8d;">Priority Level:</td>
                        <td style="padding: 5px;"><b style="color: {priority_color};">{priority}</b></td>
                    </tr>
                    <tr>
                        <td style="padding: 5px; color: #7f8c8d;">Target Delivery Time:</td>
                        <td style="padding: 5px;"><b>{delivery_time}</b></td>
                    </tr>
                    <tr>
                        <td style="padding: 5px; color: #7f8c8d;">Expected Daily Orders:</td>
                        <td style="padding: 5px;"><b>{int(order_count / 30) if isinstance(order_count, (int, float)) else "N/A"}</b></td>
                    </tr>
                </table>
            </div>
            
            <div style="margin-bottom: 20px;">
                <h3 style="color: #2c3e50; border-bottom: 1px solid #ddd; padding-bottom: 8px;">
                    Service Recommendations
                </h3>
        """
        
        # Add tier-specific recommendations with enhanced formatting
        if row['tier'] == 'Tier 1':
            desc += """
                <div style="display: flex; margin-bottom: 10px;">
                    <div style="width: 30px; text-align: center; margin-right: 10px;">📦</div>
                    <div>Schedule multiple deliveries per day (up to 4x daily)</div>
                </div>
                <div style="display: flex; margin-bottom: 10px;">
                    <div style="width: 30px; text-align: center; margin-right: 10px;">📊</div>
                    <div>Maintain priority stock levels with real-time inventory tracking</div>
                </div>
                <div style="display: flex; margin-bottom: 10px;">
                    <div style="width: 30px; text-align: center; margin-right: 10px;">🔬</div>
                    <div>Use as test location for new services and products</div>
                </div>
                <div style="display: flex; margin-bottom: 10px;">
                    <div style="width: 30px; text-align: center; margin-right: 10px;">⏱️</div>
                    <div>Target 30-minute delivery window for all orders</div>
                </div>
            """
        elif row['tier'] == 'Tier 2':
            desc += """
                <div style="display: flex; margin-bottom: 10px;">
                    <div style="width: 30px; text-align: center; margin-right: 10px;">🚚</div>
                    <div>Maintain regular daily delivery schedule (1-2 deliveries per day)</div>
                </div>
                <div style="display: flex; margin-bottom: 10px;">
                    <div style="width: 30px; text-align: center; margin-right: 10px;">📋</div>
                    <div>Monitor stock levels once per day with automated alerts</div>
                </div>
                <div style="display: flex; margin-bottom: 10px;">
                    <div style="width: 30px; text-align: center; margin-right: 10px;">🔄</div>
                    <div>Implement batch processing for orders to optimize routes</div>
                </div>
                <div style="display: flex; margin-bottom: 10px;">
                    <div style="width: 30px; text-align: center; margin-right: 10px;">⏱️</div>
                    <div>Target 1-2 hour delivery window with SMS notifications</div>
                </div>
            """
        else:  # Tier 3
            desc += """
                <div style="display: flex; margin-bottom: 10px;">
                    <div style="width: 30px; text-align: center; margin-right: 10px;">📅</div>
                    <div>Optimize delivery schedule with dedicated routes 3-4x weekly</div>
                </div>
                <div style="display: flex; margin-bottom: 10px;">
                    <div style="width: 30px; text-align: center; margin-right: 10px;">📦</div>
                    <div>Consider forward stocking of high-demand items (min 2-week supply)</div>
                </div>
                <div style="display: flex; margin-bottom: 10px;">
                    <div style="width: 30px; text-align: center; margin-right: 10px;">🔍</div>
                    <div>Evaluate for potential satellite dark store location if order volume grows</div>
                </div>
                <div style="display: flex; margin-bottom: 10px;">
                    <div style="width: 30px; text-align: center; margin-right: 10px;">⏱️</div>
                    <div>Use wider delivery windows (2+ hours) with advanced scheduling</div>
                </div>
            """
        
        # Add volume-specific recommendations with enhanced formatting
        if row['is_top30']:
            desc += """
                <h3 style="color: #8e44ad; border-bottom: 1px solid #ddd; padding-bottom: 8px; margin-top: 20px;">
                    HIGH VOLUME BRANCH - Special Considerations
                </h3>
                
                <div style="display: flex; margin-bottom: 10px;">
                    <div style="width: 30px; text-align: center; margin-right: 10px;">🔔</div>
                    <div><b>CRITICAL:</b> Ensure all orders are fulfilled completely with 99.9% accuracy target</div>
                </div>
                <div style="display: flex; margin-bottom: 10px;">
                    <div style="width: 30px; text-align: center; margin-right: 10px;">🚗</div>
                    <div>Assign dedicated delivery resources with backup drivers on standby</div>
                </div>
                <div style="display: flex; margin-bottom: 10px;">
                    <div style="width: 30px; text-align: center; margin-right: 10px;">📱</div>
                    <div>Implement automated customer satisfaction tracking with daily review</div>
                </div>
                <div style="display: flex; margin-bottom: 10px;">
                    <div style="width: 30px; text-align: center; margin-right: 10px;">👤</div>
                    <div>Assign a dedicated branch success manager to monitor operations</div>
                </div>
            """
            
            # Add special warning for high-volume Tier 3 branches
            if row['tier'] == 'Tier 3':
                desc += """
                <div style="background-color: #FDEBD0; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 5px solid #E67E22;">
                    <h4 style="color: #E67E22; margin-top: 0;">⚠️ Long-Distance High Volume Warning</h4>
                    <p style="margin-bottom: 0;">This branch is both high-volume AND long-distance, creating special logistics challenges. Consider establishing a satellite distribution point or specialized delivery schedule to maintain service levels.</p>
                </div>
                """
        
        # Close HTML structure
        desc += """
            </div>
            
            <div style="background-color: #f9f9f9; padding: 15px; border-radius: 8px; margin-top: 20px;">
                <h3 style="color: #2c3e50; margin-top: 0;">Key Performance Indicators</h3>
                <div style="display: flex; justify-content: space-between; text-align: center;">
                    <div style="flex: 1;">
                        <div style="font-size: 14px; color: #7f8c8d;">Target Accuracy</div>
                        <div style="font-size: 18px; font-weight: bold;">99%</div>
                    </div>
                    <div style="flex: 1;">
                        <div style="font-size: 14px; color: #7f8c8d;">On-Time Delivery</div>
                        <div style="font-size: 18px; font-weight: bold;">95%</div>
                    </div>
                    <div style="flex: 1;">
                        <div style="font-size: 14px; color: #7f8c8d;">Customer Rating</div>
                        <div style="font-size: 18px; font-weight: bold;">4.8/5</div>
                    </div>
                </div>
            </div>
            
            <div style="margin-top: 20px; font-size: 12px; color: #7f8c8d; text-align: right; border-top: 1px solid #eee; padding-top: 10px;">
                Generated: """ + datetime.now().strftime('%B %d, %Y') + """<br>
                Wasfaty Analytics System v2.0
            </div>
        </div>
        """
        
        # Select the appropriate style based on tier and volume
        if row['tier'] == 'Tier 1':
            folder = folder_tier1
            style_map = tier1_styles[f"tier1_{size_category}"]
            added_by_tier["Tier 1"] += 1
            if row['is_top30']:
                top30_by_tier["Tier 1"] += 1
        elif row['tier'] == 'Tier 2':
            folder = folder_tier2
            style_map = tier2_styles[f"tier2_{size_category}"]
            added_by_tier["Tier 2"] += 1
            if row['is_top30']:
                top30_by_tier["Tier 2"] += 1
        else:  # Tier 3
            folder = folder_tier3
            style_map = tier3_styles[f"tier3_{size_category}"]
            added_by_tier["Tier 3"] += 1
            if row['is_top30']:
                top30_by_tier["Tier 3"] += 1
        
        # Create the branch point with enhanced name
        pnt = folder.newpoint(name=f"Branch {row['branch_license']} ({area})")
        pnt.coords = [(row['longitude'], row['latitude'], 0)]  # KML uses (lon, lat, alt) format
        pnt.stylemap = style_map
        pnt.description = desc
        
        # Make point clamped to ground - we don't need extrude since it's a flat point marker
        # Note: SimplekML Point doesn't directly use extrude/altitudemode like the original KML spec
        # Instead we use the altitude=0 and rely on style options for visualization
        
        # Add to top branches KML if it's in the top 30
        if row['is_top30']:
            top_style = top30_styles[f"top30_{size_category}"]
            top_pnt = kml_top_branches.newpoint(name=f"TOP 30 - Branch {row['branch_license']} ({area})")
            top_pnt.coords = [(row['longitude'], row['latitude'], 0)]  # KML uses (lon, lat, alt) format
            top_pnt.stylemap = top_style
            top_pnt.description = desc
            # No need for extrude and altitudemode settings for point placemarks in simplekml

    # Add a legend folder to each KML
    legend_folder1 = kml_all_branches.newfolder(name="Map Legend & Guide")
    
    # Calculate additional statistics for rich insights
    if order_col in branch_df.columns:
        tier1_avg_orders = branch_df[branch_df['tier'] == 'Tier 1'][order_col].mean() if len(branch_df[branch_df['tier'] == 'Tier 1']) > 0 else 0
        tier2_avg_orders = branch_df[branch_df['tier'] == 'Tier 2'][order_col].mean() if len(branch_df[branch_df['tier'] == 'Tier 2']) > 0 else 0
        tier3_avg_orders = branch_df[branch_df['tier'] == 'Tier 3'][order_col].mean() if len(branch_df[branch_df['tier'] == 'Tier 3']) > 0 else 0
        
        # Calculate average delivery time estimates by tier
        tier1_avg_time = "~25 minutes"
        tier2_avg_time = "~1 hour 10 minutes"
        tier3_avg_time = "~2 hours 40 minutes"
    else:
        tier1_avg_orders = tier2_avg_orders = tier3_avg_orders = "N/A"
        tier1_avg_time = tier2_avg_time = tier3_avg_time = "N/A"

    # Calculate Tier 3 far branches (beyond 25km) for satellite recommendation
    far_branches = len(branch_df[branch_df['distance_km'] > 25])
    high_vol_far_branches = len(branch_df[(branch_df['distance_km'] > 25) & branch_df['is_top30']]) if 'is_top30' in branch_df.columns else 0
    
    legend_folder1.description = f"""
    <div style="font-family: Arial, sans-serif; max-width: 800px;">
        <h2 style="color: #2c3e50; text-align: center; border-bottom: 2px solid #3498db; padding-bottom: 8px;">
            Wasfaty Pharmacy Branch Network - Interactive Legend & Operations Guide
        </h2>
        
        <div style="display: flex; flex-wrap: wrap; justify-content: space-between; margin: 20px 0;">
            <div style="flex-basis: 32%; background-color: #e8f8e8; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                <h3 style="color: #27ae60; margin-top: 0; text-align: center; border-bottom: 1px solid #27ae60; padding-bottom: 8px;">Tier 1 Branches</h3>
                <div style="display: flex; justify-content: center; margin: 10px 0;">
                    <div style="width: 30px; height: 30px; background-color: #00FF00; border-radius: 50%;"></div>
                </div>
                <div style="text-align: center; margin-bottom: 10px;">
                    <b style="font-size: 24px;">{added_by_tier["Tier 1"]}</b><br>
                    <span style="color: #7f8c8d;">branches</span>
                </div>
                <ul style="padding-left: 20px; margin-bottom: 5px;">
                    <li>≤5km from dark store</li>
                    <li>Avg. delivery: {tier1_avg_time}</li>
                    <li>Avg. volume: {tier1_avg_orders if isinstance(tier1_avg_orders, str) else f"{tier1_avg_orders:.1f}"}</li>
                    <li>Top 30: {top30_by_tier["Tier 1"]} branches</li>
                </ul>
                <div style="background-color: rgba(39, 174, 96, 0.15); padding: 10px; border-radius: 5px; margin-top: 10px;">
                    <b style="color: #27ae60;">Operations Note:</b> Prime for rapid delivery services (30 min), multiple daily deliveries, and new service testing.
                </div>
            </div>
            
            <div style="flex-basis: 32%; background-color: #e8f0f8; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                <h3 style="color: #2980b9; margin-top: 0; text-align: center; border-bottom: 1px solid #2980b9; padding-bottom: 8px;">Tier 2 Branches</h3>
                <div style="display: flex; justify-content: center; margin: 10px 0;">
                    <div style="width: 30px; height: 30px; background-color: #0000FF; border-radius: 50%;"></div>
                </div>
                <div style="text-align: center; margin-bottom: 10px;">
                    <b style="font-size: 24px;">{added_by_tier["Tier 2"]}</b><br>
                    <span style="color: #7f8c8d;">branches</span>
                </div>
                <ul style="padding-left: 20px; margin-bottom: 5px;">
                    <li>5-15km from dark store</li>
                    <li>Avg. delivery: {tier2_avg_time}</li>
                    <li>Avg. volume: {tier2_avg_orders if isinstance(tier2_avg_orders, str) else f"{tier2_avg_orders:.1f}"}</li>
                    <li>Top 30: {top30_by_tier["Tier 2"]} branches</li>
                </ul>
                <div style="background-color: rgba(41, 128, 185, 0.15); padding: 10px; border-radius: 5px; margin-top: 10px;">
                    <b style="color: #2980b9;">Operations Note:</b> Standard 1-2 hour delivery window, daily restocking, efficient batch processing recommended.
                </div>
            </div>
            
            <div style="flex-basis: 32%; background-color: #f8e8e8; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                <h3 style="color: #c0392b; margin-top: 0; text-align: center; border-bottom: 1px solid #c0392b; padding-bottom: 8px;">Tier 3 Branches</h3>
                <div style="display: flex; justify-content: center; margin: 10px 0;">
                    <div style="width: 30px; height: 30px; background-color: #FF0000; border-radius: 50%;"></div>
                </div>
                <div style="text-align: center; margin-bottom: 10px;">
                    <b style="font-size: 24px;">{added_by_tier["Tier 3"]}</b><br>
                    <span style="color: #7f8c8d;">branches</span>
                </div>
                <ul style="padding-left: 20px; margin-bottom: 5px;">
                    <li>>15km from dark store</li>
                    <li>Avg. delivery: {tier3_avg_time}</li>
                    <li>Avg. volume: {tier3_avg_orders if isinstance(tier3_avg_orders, str) else f"{tier3_avg_orders:.1f}"}</li>
                    <li>Top 30: {top30_by_tier["Tier 3"]} branches</li>
                </ul>
                <div style="background-color: rgba(192, 57, 43, 0.15); padding: 10px; border-radius: 5px; margin-top: 10px;">
                    <b style="color: #c0392b;">Operations Note:</b> Extended delivery windows (2+ hours), optimized routing, forward stocking recommended.
                </div>
            </div>
        </div>
        
        <div style="margin: 25px 0; border: 1px solid #ddd; border-radius: 8px; overflow: hidden;">
            <div style="background-color: #3498db; color: white; padding: 15px; text-align: center;">
                <h3 style="margin: 0;">Interactive Map Legend Guide</h3>
            </div>
            <div style="padding: 20px;">
                <div style="margin-bottom: 20px;">
                    <h4 style="color: #2c3e50; border-bottom: 1px solid #eee; padding-bottom: 8px;">Marker Size Guide (by Order Volume)</h4>
                    <div style="display: flex; align-items: center; margin-bottom: 15px;">
                        <div style="flex: 1; display: flex; justify-content: space-between;">
                            <div style="display: inline-block; width: 10px; height: 10px; background-color: #777; border-radius: 50%;"></div>
                            <div style="display: inline-block; width: 13px; height: 13px; background-color: #777; border-radius: 50%;"></div>
                            <div style="display: inline-block; width: 16px; height: 16px; background-color: #777; border-radius: 50%;"></div>
                            <div style="display: inline-block; width: 19px; height: 19px; background-color: #777; border-radius: 50%;"></div>
                            <div style="display: inline-block; width: 22px; height: 22px; background-color: #777; border-radius: 50%;"></div>
                        </div>
                        <div style="flex: 1; display: flex; justify-content: space-between; margin-left: 10px; color: #7f8c8d; font-size: 12px;">
                            <div>Lowest</div>
                            <div></div>
                            <div>Medium</div>
                            <div></div>
                            <div>Highest</div>
                        </div>
                    </div>
                    <p style="color: #7f8c8d; font-style: italic; font-size: 13px; margin-top: 0;">
                        Marker sizes are proportionally scaled to represent order volume. Larger markers indicate branches with higher order volumes.
                    </p>
                </div>
                
                <div style="margin-bottom: 20px;">
                    <h4 style="color: #2c3e50; border-bottom: 1px solid #eee; padding-bottom: 8px;">Top 30 High Volume Branches</h4>
                    <div style="display: flex; align-items: center;">
                        <div style="margin-right: 15px;">
                            <div style="width: 30px; height: 30px; background-color: #800080; clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);"></div>
                        </div>
                        <div>
                            <p style="margin: 0;"><b style="color: #8e44ad;">Purple stars</b> mark the top 30 branches by order volume</p>
                            <p style="margin: 5px 0 0 0; color: #7f8c8d; font-size: 13px;">These high-priority branches account for approximately {top30_pct:.1f}% of total order volume</p>
                        </div>
                    </div>
                    
                    <table style="width: 100%; border-collapse: collapse; margin-top: 10px; font-size: 14px;">
                        <tr style="background-color: #f2e5f9;">
                            <th style="padding: 8px; text-align: left; border: 1px solid #ddd;">Tier</th>
                            <th style="padding: 8px; text-align: center; border: 1px solid #ddd;">Count</th>
                            <th style="padding: 8px; text-align: left; border: 1px solid #ddd;">Operational Impact</th>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd; color: #27ae60;"><b>Tier 1 Top 30</b></td>
                            <td style="padding: 8px; border: 1px solid #ddd; text-align: center;">{top30_by_tier["Tier 1"]}</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">Prioritize for premium service & delivery resources</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd; color: #2980b9;"><b>Tier 2 Top 30</b></td>
                            <td style="padding: 8px; border: 1px solid #ddd; text-align: center;">{top30_by_tier["Tier 2"]}</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">Optimize routes & dedicated delivery personnel</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd; color: #c0392b;"><b>Tier 3 Top 30</b></td>
                            <td style="padding: 8px; border: 1px solid #ddd; text-align: center;">{top30_by_tier["Tier 3"]}</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">Requires special logistics planning & forward stocking</td>
                        </tr>
                    </table>
                </div>
                
                <div>
                    <h4 style="color: #2c3e50; border-bottom: 1px solid #eee; padding-bottom: 8px;">Service Boundaries & Operational Planning</h4>
                    <table style="width: 100%; border-collapse: collapse; margin-top: 10px; font-size: 14px;">
                        <tr style="background-color: #ecf0f1;">
                            <th style="padding: 8px; text-align: left; border: 1px solid #ddd;">Boundary</th>
                            <th style="padding: 8px; text-align: center; border: 1px solid #ddd;">Radius</th>
                            <th style="padding: 8px; text-align: left; border: 1px solid #ddd;">Service Level</th>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd;"><b style="color: #27ae60;">Tier 1 Circle</b> (Green)</td>
                            <td style="padding: 8px; border: 1px solid #ddd; text-align: center;">5 km</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">Premium 30-minute delivery zone</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd;"><b style="color: #2980b9;">Tier 2 Circle</b> (Blue)</td>
                            <td style="padding: 8px; border: 1px solid #ddd; text-align: center;">15 km</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">Standard 1-2 hour delivery zone</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        
        <div style="background-color: #FDEBD0; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 5px solid #F39C12;">
            <h3 style="color: #E67E22; margin-top: 0;">Strategic Recommendations</h3>
            
            <div style="margin-top: 10px;">
                <h4 style="color: #d35400; margin-bottom: 5px;">Resource Allocation Strategy</h4>
                <ul style="margin-top: 5px;">
                    <li>Allocate {int(top30_pct/100 * 50) + 50}% of delivery resources to Top 30 branches</li>
                    <li>Implement rapid fulfillment protocols for all Tier 1 branches</li>
                    <li>Consider dedicated delivery personnel for high-volume Tier 2 branches</li>
                </ul>
            </div>
            
            <div style="margin-top: 10px;">
                <h4 style="color: #d35400; margin-bottom: 5px;">Network Expansion Insights</h4>
                <ul style="margin-top: 5px;">
                    <li>Consider satellite hub for far branches ({far_branches} branches >25km away)</li>
                    <li>{high_vol_far_branches} high-volume branches in distant areas merit special attention</li>
                    <li>Monitor growth in Tier 3 areas to identify emerging demand centers</li>
                </ul>
            </div>
        </div>
        
        <div style="background-color: #EBF5FB; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #3498db; margin-top: 0;">How to Use This Map</h3>
            <ol>
                <li><b>Click on any marker</b> to see detailed branch information and specific operational recommendations</li>
                <li><b>Use the left sidebar</b> to toggle different layers (tiers, boundaries, etc.)</li>
                <li><b>View the Top 30 Data Table</b> in the sidebar for a sortable list of priority branches</li>
                <li><b>Export areas of interest</b> using the "Share" feature in Google Maps/Earth</li>
            </ol>
            <p style="font-style: italic; margin-bottom: 0;">For specific branch logistics questions or custom analysis needs, please contact the analytics team.</p>
        </div>
        
        <div style="margin-top: 20px; font-size: 12px; color: #7f8c8d; text-align: right; border-top: 1px solid #eee; padding-top: 10px;">
            Generated: {datetime.now().strftime('%B %d, %Y at %H:%M')}<br>
            Wasfaty Analytics System v2.0
        </div>
    </div>
    """
    
    legend_folder2 = kml_top_branches.newfolder(name="Map Legend & Guide")
    legend_folder2.description = legend_folder1.description

    # Add a table of top 30 branches to the top30 KML
    table_folder = kml_top_branches.newfolder(name="Top 30 Branches Table")
    
    # Get top 30 branches sorted by order volume
    if order_col:
        top30_branches = branch_df[branch_df['is_top30']].sort_values(by=order_col, ascending=False)
        
        # Create enhanced HTML table with filtering and sortable columns
        table_html = f"""
        <div style="font-family: Arial, sans-serif; max-width: 900px;">
            <div style="background-color: #f2e5f9; padding: 20px; border-radius: 10px; border: 1px solid #8e44ad; margin-bottom: 20px;">
                <h2 style="color: #8e44ad; text-align: center; margin-top: 0; padding-bottom: 10px; border-bottom: 2px solid #8e44ad;">
                    Top 30 High-Volume Pharmacy Branches - Operations Priority List
                </h2>
                <div style="display: flex; justify-content: space-between; text-align: center; margin-top: 15px;">
                    <div>
                        <div style="font-size: 14px; color: #7f8c8d;">Total Order Volume</div>
                        <div style="font-size: 22px; font-weight: bold;">{top30_branches[order_col].sum() if order_col in top30_branches.columns else 'N/A'}</div>
                    </div>
                    <div>
                        <div style="font-size: 14px; color: #7f8c8d;">% of Total Orders</div>
                        <div style="font-size: 22px; font-weight: bold;">{top30_pct:.1f}%</div>
                    </div>
                    <div>
                        <div style="font-size: 14px; color: #7f8c8d;">Avg. Distance</div>
                        <div style="font-size: 22px; font-weight: bold;">{top30_branches['distance_km'].mean():.1f} km</div>
                    </div>
                </div>
            </div>
            
            <div style="background-color: #FDEBD0; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 5px solid #F39C12;">
                <h3 style="color: #E67E22; margin-top: 0;">How to Use This Table</h3>
                <p style="margin-top: 5px; margin-bottom: 0;">
                    <b>1.</b> Use this table to identify and prioritize your highest-volume branches for focused operational improvements.<br>
                    <b>2.</b> Click on branch names in the table to navigate to their locations on the map.<br>
                    <b>3.</b> Note the tier distribution to understand the relationship between distance and order volume.<br>
                    <b>4.</b> Pay special attention to "Critical Action" recommendations for immediate operational improvements.
                </p>
            </div>
            
            <div style="margin-bottom: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 8px; border: 1px solid #ddd;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                    <div>
                        <span style="font-weight: bold; margin-right: 10px;">Quick Filters:</span>
                        <span style="background-color: #e8f8e8; padding: 5px 10px; border-radius: 15px; border: 1px solid #27ae60; margin-right: 5px; cursor: pointer;">Tier 1 ({top30_by_tier["Tier 1"]})</span>
                        <span style="background-color: #e8f0f8; padding: 5px 10px; border-radius: 15px; border: 1px solid #2980b9; margin-right: 5px; cursor: pointer;">Tier 2 ({top30_by_tier["Tier 2"]})</span>
                        <span style="background-color: #f8e8e8; padding: 5px 10px; border-radius: 15px; border: 1px solid #c0392b; cursor: pointer;">Tier 3 ({top30_by_tier["Tier 3"]})</span>
                    </div>
                    <div>
                        <span style="color: #7f8c8d;">Sort by:</span>
                        <select style="padding: 5px; border-radius: 4px; border: 1px solid #ddd; margin-left: 5px;">
                            <option>Rank (Default)</option>
                            <option>Order Volume (High to Low)</option>
                            <option>Distance (Near to Far)</option>
                            <option>Area Name</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <table style="width:100%; border-collapse: collapse; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border-radius: 8px; overflow: hidden;">
                <thead>
                    <tr style="background-color: #8e44ad; color: white;">
                        <th style="padding: 12px; text-align: center; border: 1px solid #7d2ba3;">#</th>
                        <th style="padding: 12px; text-align: left; border: 1px solid #7d2ba3;">Branch License</th>
                        <th style="padding: 12px; text-align: left; border: 1px solid #7d2ba3;">Area</th>
                        <th style="padding: 12px; text-align: right; border: 1px solid #7d2ba3;">Order Volume</th>
                        <th style="padding: 12px; text-align: right; border: 1px solid #7d2ba3;">Distance (km)</th>
                        <th style="padding: 12px; text-align: center; border: 1px solid #7d2ba3;">Tier</th>
                        <th style="padding: 12px; text-align: left; border: 1px solid #7d2ba3;">Critical Action</th>
                    </tr>
                </thead>
                <tbody>
        """
        
        # Add rows for each branch with advanced styling and critical actions
        for i, (_, row) in enumerate(top30_branches.iterrows(), 1):
            area = row.get('area', 'N/A')
            order_count = row.get(order_col, 'N/A')
            
            # Set row style based on tier
            if row['tier'] == 'Tier 1':
                bg_color = "#e8f8e8"  # Light green
                tier_text_color = "#27ae60"
                tier_icon = "🟢"
                if isinstance(order_count, (int, float)) and order_count > 0:
                    critical_action = "Premium 30min delivery SLA"
                else:
                    critical_action = "Optimize stockouts (priority)"
                    
            elif row['tier'] == 'Tier 2':
                bg_color = "#e8f0f8"  # Light blue
                tier_text_color = "#2980b9"
                tier_icon = "🔵"
                if row['distance_km'] < 10:
                    critical_action = "60min delivery target"
                else:
                    critical_action = "Route optimization needed"
                    
            else:  # Tier 3
                bg_color = "#f8e8e8"  # Light red
                tier_text_color = "#c0392b"
                tier_icon = "🔴"
                if row['distance_km'] > 25:
                    critical_action = "Satellite hub candidate!"
                else:
                    critical_action = "2hr delivery window, buffer stock"
            
            # Add highlighting for top 10 branches
            if i <= 10:
                row_style = f"background-color: {bg_color}; font-weight: bold;"
                rank_badge = f'<div style="display: inline-block; background-color: #8e44ad; color: white; width: 24px; height: 24px; border-radius: 50%; text-align: center; line-height: 24px;">{i}</div>'
            else:
                row_style = f"background-color: {bg_color};"
                rank_badge = str(i)
            
            table_html += f"""
                <tr style="{row_style}">
                    <td style="padding: 10px; text-align: center; border: 1px solid #ddd;">{rank_badge}</td>
                    <td style="padding: 10px; text-align: left; border: 1px solid #ddd;">
                        <a href="#" style="color: #8e44ad; text-decoration: none; font-weight: bold;">{row['branch_license']}</a>
                    </td>
                    <td style="padding: 10px; text-align: left; border: 1px solid #ddd;">{area}</td>
                    <td style="padding: 10px; text-align: right; border: 1px solid #ddd;">{order_count}</td>
                    <td style="padding: 10px; text-align: right; border: 1px solid #ddd;">{row['distance_km']:.1f}</td>
                    <td style="padding: 10px; text-align: center; border: 1px solid #ddd;">
                        <span style="color: {tier_text_color}; font-weight: bold;">{tier_icon} {row['tier']}</span>
                    </td>
                    <td style="padding: 10px; text-align: left; border: 1px solid #ddd;">
                        <span style="font-size: 13px;">{critical_action}</span>
                    </td>
                </tr>
            """
        
        table_html += """
                </tbody>
            </table>
            
            <div style="margin-top: 25px; background-color: #EBF5FB; padding: 15px; border-radius: 8px; border: 1px solid #3498db;">
                <h3 style="color: #2980b9; margin-top: 0;">Strategic Operational Recommendations</h3>
                <ol style="margin-bottom: 0;">
                    <li><b>Resource Allocation:</b> Prioritize delivery resources to these branches with dedicated fleet designation</li>
                    <li><b>Inventory Management:</b> Increase safety stock levels by 20% for these branches to prevent stockouts</li>
                    <li><b>Performance Monitoring:</b> Implement daily KPI tracking for these branches (order accuracy, on-time delivery)</li>
                    <li><b>Customer Experience:</b> Deploy priority customer service team for orders from these branches</li>
                    <li><b>Continuous Improvement:</b> Schedule monthly operations reviews focused on these 30 branches</li>
                </ol>
            </div>
            
            <div style="margin-top: 20px; font-size: 12px; color: #7f8c8d; text-align: right; border-top: 1px solid #eee; padding-top: 10px;">
                Generated: """ + datetime.now().strftime('%B %d, %Y at %H:%M') + """<br>
                Wasfaty Analytics System v2.0
            </div>
        </div>
        """
        
        # Add the table information as a document description in the Top 30 KML
        # Instead of creating a point, we'll set this information as the folder description
        table_folder.description = table_html

    # Save the KML files with more descriptive names
    timestamp = datetime.now().strftime('%Y%m%d')
    kml_all_branches_path = os.path.join(output_dir, f"wasfaty_all_branches_by_tier_{timestamp}.kml")
    kml_top_branches_path = os.path.join(output_dir, f"wasfaty_top30_branches_{timestamp}.kml")

    kml_all_branches.save(kml_all_branches_path)
    kml_top_branches.save(kml_top_branches_path)

    # Print detailed summary
    print("\n" + "="*70)
    print(f"ENHANCED KML EXPORT COMPLETED SUCCESSFULLY")
    print("="*70)
    
    print(f"\nAll branches KML file saved as: {kml_all_branches_path}")
    print(f"Top 30 branches KML file saved as: {kml_top_branches_path}")
    
    print("\nBranches exported by tier:")
    for tier, count in added_by_tier.items():
        print(f"  {tier}: {count} branches")

    print("\nTop 30 branches by tier:")
    for tier, count in top30_by_tier.items():
        print(f"  {tier}: {count} branches")
        
    print("\nEnhanced visualization features:")
    print("  - Rich HTML information popups with branch details")
    print("  - Marker sizes proportional to order volume")
    print("  - Color coding by tier with improved visibility")
    print("  - Service area boundaries with detailed information")
    print("  - Top 30 branches highlighted with purple stars")
    print("  - Built-in legends and data tables")
    print("  - Interactive descriptions with recommendations")
    
    return kml_all_branches_path, kml_top_branches_path

def main():
    # Coordinates of the dark store - update these as needed
    DARK_STORE_LAT, DARK_STORE_LON = 24.68769, 46.80032
    
    try:
        # Try to load branch data from the correct location
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

        try:
            branch_data_path = os.path.join(project_root, 'data', 'raw', 'branch_data.csv')
            branch_df = load_branch_data(branch_data_path)
            print(f"Loaded branch data from {branch_data_path}")
        except FileNotFoundError:
            print("branch_data.csv not found in data/raw/. Looking for branch_locs.csv...")
            branch_locs_path = os.path.join(project_root, 'data', 'raw', 'branch_locs.csv')
            branch_df = load_branch_data(branch_locs_path)
            print(f"Loaded branch data from {branch_locs_path}")
        
        # Assign tiers based on distance
        branch_df = assign_tiers(branch_df, DARK_STORE_LAT, DARK_STORE_LON)

        # Set up output directory
        output_dir = os.path.join(project_root, 'outputs', 'kml')
        os.makedirs(output_dir, exist_ok=True)

        # Generate KML files
        generate_kml_files(branch_df, DARK_STORE_LAT, DARK_STORE_LON, output_dir)
        
        print("\nInstructions:")
        print("1. To view in Google Maps: ")
        print("   - Go to https://www.google.com/maps")
        print("   - Click the menu (≡) > Your places > Maps > Create Map")
        print("   - Click 'Import' and upload the KML files")
        print("2. To share with operations team:")
        print("   - After importing to Google Maps, click 'Share' and send the link")
        print("   - Or directly share the KML files, which can be opened in Google Earth")
        
    except Exception as e:
        print(f"Error generating KML files: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
