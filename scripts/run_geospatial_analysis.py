#!/usr/bin/env python3
"""
Quick execution script for Wasfaty Geospatial Analysis
Runs the complete analysis pipeline and generates outputs
"""

import os
import sys
import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# Add project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from src.data_utils import clean_merged_logs
from src.geospatial_utils import (
    add_h3_indices, calculate_h3_demand_density, identify_coverage_gaps,
    create_temporal_heatmap_data, calculate_branch_performance_metrics,
    create_supply_demand_map, generate_operational_insights
)

def main():
    """
    Execute the complete geospatial analysis pipeline
    """
    print("🚀 Starting Wasfaty Geospatial Analysis...")
    print("=" * 60)
    
    # 1. Load and prepare data
    print("\n📊 Step 1: Loading and cleaning data...")
    data_path = os.path.join(project_root, 'data', 'raw', 'daily_logs', 'merged_logs.csv')
    if not os.path.exists(data_path):
        print(f"❌ Data file not found: {data_path}")
        print("Available files in data/raw/daily_logs/:")
        logs_dir = os.path.join(project_root, 'data', 'raw', 'daily_logs')
        if os.path.exists(logs_dir):
            for f in os.listdir(logs_dir):
                if f.endswith('.csv'):
                    print(f"  - {f}")
        return None

    df = clean_merged_logs(data_path)
    df_spatial = df.dropna(subset=['latitude', 'longitude']).copy()
    
    print(f"✅ Loaded {len(df_spatial):,} orders with valid coordinates")
    print(f"📅 Date range: {df_spatial['delivery_date'].min()} to {df_spatial['delivery_date'].max()}")
    print(f"🏪 Unique branches: {df_spatial['branch_license'].nunique()}")
    print(f"🏘️ Unique areas: {df_spatial['area'].nunique()}")
    
    # 2. H3 Spatial Analysis
    print("\n🔶 Step 2: H3 Hexagonal Indexing...")
    df_h3 = add_h3_indices(df_spatial, resolutions=[7, 8, 9])
    
    # Calculate demand density for different resolutions
    h3_demand_res7 = calculate_h3_demand_density(df_h3, 'h3_res_7')
    h3_demand_res8 = calculate_h3_demand_density(df_h3, 'h3_res_8')
    h3_demand_res9 = calculate_h3_demand_density(df_h3, 'h3_res_9')
    
    print(f"✅ H3 Analysis Complete:")
    print(f"   • Resolution 7: {len(h3_demand_res7)} hexagons")
    print(f"   • Resolution 8: {len(h3_demand_res8)} hexagons")
    print(f"   • Resolution 9: {len(h3_demand_res9)} hexagons")
    
    # 3. Branch Performance Analysis
    print("\n🏪 Step 3: Branch Performance Analysis...")
    branch_metrics = calculate_branch_performance_metrics(df_h3)
    
    top_branch = branch_metrics.loc[branch_metrics['total_orders'].idxmax()]
    print(f"✅ Branch Analysis Complete:")
    print(f"   • Top performer: {top_branch['branch_license']} ({top_branch['total_orders']} orders)")
    print(f"   • Average orders/branch: {branch_metrics['total_orders'].mean():.1f}")
    print(f"   • Average pickup ratio: {branch_metrics['pickup_ratio'].mean():.2%}")
    
    # 4. Coverage Gap Analysis
    print("\n🔍 Step 4: Coverage Gap Analysis...")
    branch_locations = branch_metrics[['branch_license', 'latitude', 'longitude']].copy()
    coverage_gaps = identify_coverage_gaps(
        demand_df=h3_demand_res8,
        branch_df=branch_locations,
        max_distance_km=5.0
    )
    
    if not coverage_gaps.empty:
        print(f"⚠️ Coverage Gaps Identified:")
        print(f"   • Total gaps: {len(coverage_gaps)}")
        print(f"   • Underserved orders: {coverage_gaps['order_count'].sum():,}")
        print(f"   • Average gap distance: {coverage_gaps['distance_to_nearest_branch'].mean():.2f} km")
    else:
        print("✅ No significant coverage gaps found!")
    
    # 5. Generate Interactive Map
    print("\n🗺️ Step 5: Creating Interactive Map...")
    supply_demand_map = create_supply_demand_map(df_h3, h3_demand_res8, branch_metrics)

    # Save to outputs directory
    output_path = os.path.join(project_root, 'outputs', 'maps', 'supply_demand_analysis.html')
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    supply_demand_map.save(output_path)
    print(f"✅ Interactive map saved as '{output_path}'")
    
    # 6. Generate Operational Insights
    print("\n🎯 Step 6: Generating Operational Insights...")
    insights = generate_operational_insights(
        df=df_h3,
        h3_demand=h3_demand_res8,
        branch_metrics=branch_metrics,
        coverage_gaps=coverage_gaps if not coverage_gaps.empty else pd.DataFrame()
    )
    
    # Display key insights
    print("\n" + "=" * 60)
    print("🎯 KEY OPERATIONAL INSIGHTS")
    print("=" * 60)
    
    print(f"\n📊 DEMAND PATTERNS:")
    print(f"• Peak Hour: {insights['demand_patterns']['peak_hour']}:00 ({insights['demand_patterns']['peak_hour_orders']:,} orders)")
    print(f"• Peak Day: {insights['demand_patterns']['peak_day']} ({insights['demand_patterns']['peak_day_orders']:,} orders)")
    print(f"• Total Orders: {insights['demand_patterns']['total_orders']:,}")
    
    print(f"\n🏪 SUPPLY OPTIMIZATION:")
    print(f"• Top Branches: {', '.join(insights['supply_optimization']['top_performing_branches'][:3])}")
    print(f"• Average Orders/Branch: {insights['supply_optimization']['avg_orders_per_branch']:.1f}")
    
    if 'coverage_analysis' in insights:
        print(f"\n🔍 COVERAGE ANALYSIS:")
        print(f"• Coverage Gaps: {insights['coverage_analysis']['total_coverage_gaps']}")
        print(f"• High Priority Gaps: {insights['coverage_analysis']['high_priority_gaps']}")
    
    print(f"\n💡 TOP RECOMMENDATIONS:")
    for i, rec in enumerate(insights['recommendations'][:5], 1):
        print(f"{i}. {rec}")
    
    # 7. Save Summary Report
    print(f"\n📄 Step 7: Saving Analysis Summary...")
    
    # Create summary statistics
    summary_stats = {
        'analysis_date': pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S'),
        'total_orders': len(df_h3),
        'total_branches': df_h3['branch_license'].nunique(),
        'total_areas': df_h3['area'].nunique(),
        'h3_hexagons_res8': len(h3_demand_res8),
        'coverage_gaps': len(coverage_gaps) if not coverage_gaps.empty else 0,
        'peak_hour': insights['demand_patterns']['peak_hour'],
        'peak_day': insights['demand_patterns']['peak_day'],
        'top_branch': top_branch['branch_license'],
        'avg_orders_per_branch': branch_metrics['total_orders'].mean()
    }
    
    # Save to JSON for easy access
    import json
    summary_path = os.path.join(project_root, 'outputs', 'reports', 'analysis_summary.json')
    os.makedirs(os.path.dirname(summary_path), exist_ok=True)
    with open(summary_path, 'w') as f:
        json.dump(summary_stats, f, indent=2, default=str)

    print(f"✅ Analysis summary saved as '{summary_path}'")
    
    print("\n" + "=" * 60)
    print("🎉 ANALYSIS COMPLETE!")
    print("=" * 60)
    print("\n📁 Generated Files:")
    print(f"• {output_path} - Interactive map")
    print(f"• {summary_path} - Key metrics summary")
    print("• notebooks/geospatial/advanced_geospatial_analysis.ipynb - Full analysis notebook")

    print(f"\n🚀 Next Steps:")
    print(f"1. Open {output_path} in your browser")
    print("2. Review the Jupyter notebook for detailed analysis")
    print("3. Implement the operational recommendations")
    print("4. Schedule regular analysis updates")
    
    return {
        'df_h3': df_h3,
        'h3_demand': h3_demand_res8,
        'branch_metrics': branch_metrics,
        'coverage_gaps': coverage_gaps,
        'insights': insights,
        'summary_stats': summary_stats
    }

if __name__ == "__main__":
    try:
        results = main()
        print(f"\n✅ Analysis completed successfully!")
    except Exception as e:
        print(f"\n❌ Analysis failed with error: {str(e)}")
        print("Please check your data files and dependencies.")
        raise
