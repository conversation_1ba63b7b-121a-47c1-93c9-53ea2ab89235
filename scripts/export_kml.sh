#!/bin/bash

# Run the enhanced KML export script
# This script generates detailed KML files for visualizing Wasfaty pharmacy branches
# Usage: ./export_kml.sh

echo "==================================================="
echo "   Wasfaty Enhanced KML Export Tool"
echo "==================================================="
echo

# Change to the script directory
cd "$(dirname "$0")"

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is required but could not be found"
    exit 1
fi

# Check if required Python packages are installed
python3 -c "import simplekml, pandas, numpy" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "Installing required Python packages..."
    pip install simplekml pandas numpy
fi

# Check if branch data exists
if [ ! -f branch_data.csv ] && [ ! -f branch_locs.csv ]; then
    echo "Error: No branch data found. Please ensure branch_data.csv or branch_locs.csv exists."
    exit 1
fi

# Run the KML export script
echo "Generating enhanced KML files..."
python3 generate_branch_kml.py

# Check if export was successful
if [ $? -eq 0 ]; then
    echo
    echo "==================================================="
    echo "   KML Export Complete!"
    echo "==================================================="
    echo
    echo "Next Steps:"
    echo "1. Import the KML files into Google My Maps"
    echo "2. See ENHANCED_KML_EXPORT_README.md for detailed usage instructions"
    echo
else
    echo "Error: KML export failed"
    exit 1
fi
