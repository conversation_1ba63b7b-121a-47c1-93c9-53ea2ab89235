#!/bin/bash

# Wasfaty Geospatial Analysis Setup and Execution Script
# This script sets up the environment and runs the complete analysis

echo "🚀 Wasfaty Geospatial Analysis Setup"
echo "===================================="

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required but not installed."
    exit 1
fi

echo "✅ Python 3 found: $(python3 --version)"

# Check if pip is available
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 is required but not installed."
    exit 1
fi

echo "✅ pip3 found"

# Install required packages
echo ""
echo "📦 Installing required packages..."
pip3 install -r requirements.txt

if [ $? -eq 0 ]; then
    echo "✅ All packages installed successfully!"
else
    echo "❌ Package installation failed. Please check your internet connection and try again."
    exit 1
fi

# Check if data files exist
echo ""
echo "📊 Checking data files..."

if [ ! -f "daily_logs/merged_logs.csv" ]; then
    echo "❌ merged_logs.csv not found in daily_logs/ directory"
    echo "Please ensure your log data is available before running the analysis."
    exit 1
fi

echo "✅ Data files found"

# Run the analysis
echo ""
echo "🔄 Running geospatial analysis..."
python3 run_geospatial_analysis.py

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 Analysis completed successfully!"
    echo ""
    echo "📁 Generated files:"
    echo "• supply_demand_analysis.html - Interactive map"
    echo "• analysis_summary.json - Key metrics"
    echo ""
    echo "🌐 To view the interactive map:"
    echo "open supply_demand_analysis.html"
    echo ""
    echo "📓 To run the full Jupyter analysis:"
    echo "jupyter notebook advanced_geospatial_analysis.ipynb"
else
    echo "❌ Analysis failed. Please check the error messages above."
    exit 1
fi

echo ""
echo "✅ Setup and analysis complete!"
