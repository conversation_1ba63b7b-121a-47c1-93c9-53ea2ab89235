<!DOCTYPE html>
<html>
<head>
    
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script src="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="https://netdna.bootstrapcdn.com/bootstrap/3.0.0/css/bootstrap-glyphicons.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.2.0/css/all.min.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/python-visualization/folium/folium/templates/leaflet.awesome.rotate.min.css"/>
    
            <meta name="viewport" content="width=device-width,
                initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
            <style>
                #map_916ed6245fdeec780df9bb7e80da6a82 {
                    position: relative;
                    width: 100.0%;
                    height: 100.0%;
                    left: 0.0%;
                    top: 0.0%;
                }
                .leaflet-container { font-size: 1rem; }
            </style>

            <style>html, body {
                width: 100%;
                height: 100%;
                margin: 0;
                padding: 0;
            }
            </style>

            <style>#map {
                position:absolute;
                top:0;
                bottom:0;
                right:0;
                left:0;
                }
            </style>

            <script>
                L_NO_TOUCH = false;
                L_DISABLE_3D = false;
            </script>

        
</head>
<body>
    
    
            <div class="folium-map" id="map_916ed6245fdeec780df9bb7e80da6a82" ></div>
        
</body>
<script>
    
    
            var map_916ed6245fdeec780df9bb7e80da6a82 = L.map(
                "map_916ed6245fdeec780df9bb7e80da6a82",
                {
                    center: [24.68769, 46.80032],
                    crs: L.CRS.EPSG3857,
                    ...{
  "zoom": 10,
  "zoomControl": true,
  "preferCanvas": false,
}

                }
            );

            

        
    
            var tile_layer_d3c7bab3c49abae443908f816f8c5825 = L.tileLayer(
                "https://tile.openstreetmap.org/{z}/{x}/{y}.png",
                {
  "minZoom": 0,
  "maxZoom": 19,
  "maxNativeZoom": 19,
  "noWrap": false,
  "attribution": "\u0026copy; \u003ca href=\"https://www.openstreetmap.org/copyright\"\u003eOpenStreetMap\u003c/a\u003e contributors",
  "subdomains": "abc",
  "detectRetina": false,
  "tms": false,
  "opacity": 1,
}

            );
        
    
            tile_layer_d3c7bab3c49abae443908f816f8c5825.addTo(map_916ed6245fdeec780df9bb7e80da6a82);
        
    
            var marker_e97afbf3825e0e63ecdd0e56ade98baa = L.marker(
                [24.68769, 46.80032],
                {
}
            ).addTo(map_916ed6245fdeec780df9bb7e80da6a82);
        
    
            var icon_2415d355a8a2a8027b4c4f38bb0b1e41 = L.AwesomeMarkers.icon(
                {
  "markerColor": "darkblue",
  "iconColor": "white",
  "icon": "building",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_1e82dfd674b1788c12bbe432df222b83 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_913f883ef0910a7778afa15826408a35 = $(`<div id="html_913f883ef0910a7778afa15826408a35" style="width: 100.0%; height: 100.0%;">Central Dark Store (Active)</div>`)[0];
                popup_1e82dfd674b1788c12bbe432df222b83.setContent(html_913f883ef0910a7778afa15826408a35);
            
        

        marker_e97afbf3825e0e63ecdd0e56ade98baa.bindPopup(popup_1e82dfd674b1788c12bbe432df222b83)
        ;

        
    
    
                marker_e97afbf3825e0e63ecdd0e56ade98baa.setIcon(icon_2415d355a8a2a8027b4c4f38bb0b1e41);
            
    
            var circle_4ef39eecf81d4033ef7ced312a8baf19 = L.circle(
                [24.68769, 46.80032],
                {"bubblingMouseEvents": true, "color": "blue", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.1, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 15000, "stroke": true, "weight": 3}
            ).addTo(map_916ed6245fdeec780df9bb7e80da6a82);
        
    
        var popup_18f45a108f5110c5487b72d1ba7b6d3f = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_387c98d48c3b249974190367682655db = $(`<div id="html_387c98d48c3b249974190367682655db" style="width: 100.0%; height: 100.0%;">Central Dark Store 15km Coverage</div>`)[0];
                popup_18f45a108f5110c5487b72d1ba7b6d3f.setContent(html_387c98d48c3b249974190367682655db);
            
        

        circle_4ef39eecf81d4033ef7ced312a8baf19.bindPopup(popup_18f45a108f5110c5487b72d1ba7b6d3f)
        ;

        
    
    
            var marker_4a43aca5176bb2b20d6413359a78b0e6 = L.marker(
                [24.74102, 46.87236],
                {
}
            ).addTo(map_916ed6245fdeec780df9bb7e80da6a82);
        
    
            var icon_30c08e4f095675daddfeff1f85b2a178 = L.AwesomeMarkers.icon(
                {
  "markerColor": "lightblue",
  "iconColor": "white",
  "icon": "building-o",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_a151aa1aa22f0ed587f5e8f6789ed798 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_3b923286fd18c7010780b11f093c62b3 = $(`<div id="html_3b923286fd18c7010780b11f093c62b3" style="width: 100.0%; height: 100.0%;">East Hub (Proposed)</div>`)[0];
                popup_a151aa1aa22f0ed587f5e8f6789ed798.setContent(html_3b923286fd18c7010780b11f093c62b3);
            
        

        marker_4a43aca5176bb2b20d6413359a78b0e6.bindPopup(popup_a151aa1aa22f0ed587f5e8f6789ed798)
        ;

        
    
    
                marker_4a43aca5176bb2b20d6413359a78b0e6.setIcon(icon_30c08e4f095675daddfeff1f85b2a178);
            
    
            var circle_3265afd28f08268c0d0ea33f54689486 = L.circle(
                [24.74102, 46.87236],
                {"bubblingMouseEvents": true, "color": "blue", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.1, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 15000, "stroke": true, "weight": 3}
            ).addTo(map_916ed6245fdeec780df9bb7e80da6a82);
        
    
        var popup_6703e9f9c91371a34fbfd323a3ab3080 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_10907de6ef18038e98c609a1f473306d = $(`<div id="html_10907de6ef18038e98c609a1f473306d" style="width: 100.0%; height: 100.0%;">East Hub 15km Coverage</div>`)[0];
                popup_6703e9f9c91371a34fbfd323a3ab3080.setContent(html_10907de6ef18038e98c609a1f473306d);
            
        

        circle_3265afd28f08268c0d0ea33f54689486.bindPopup(popup_6703e9f9c91371a34fbfd323a3ab3080)
        ;

        
    
    
            var marker_c038215d4c86102f58bc44787a8cfe7b = L.marker(
                [24.63987, 46.70124],
                {
}
            ).addTo(map_916ed6245fdeec780df9bb7e80da6a82);
        
    
            var icon_5c359c9f552b0c54a98764d3043ce38e = L.AwesomeMarkers.icon(
                {
  "markerColor": "lightblue",
  "iconColor": "white",
  "icon": "building-o",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_95de4819709e525cda9057a85a2a24ac = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_576c6444fe03d9997b20a83f64122877 = $(`<div id="html_576c6444fe03d9997b20a83f64122877" style="width: 100.0%; height: 100.0%;">West Hub (Proposed)</div>`)[0];
                popup_95de4819709e525cda9057a85a2a24ac.setContent(html_576c6444fe03d9997b20a83f64122877);
            
        

        marker_c038215d4c86102f58bc44787a8cfe7b.bindPopup(popup_95de4819709e525cda9057a85a2a24ac)
        ;

        
    
    
                marker_c038215d4c86102f58bc44787a8cfe7b.setIcon(icon_5c359c9f552b0c54a98764d3043ce38e);
            
    
            var circle_11bc7616ec34f49dbb406a2cdbae6aee = L.circle(
                [24.63987, 46.70124],
                {"bubblingMouseEvents": true, "color": "blue", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.1, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 15000, "stroke": true, "weight": 3}
            ).addTo(map_916ed6245fdeec780df9bb7e80da6a82);
        
    
        var popup_1394688be6a4b88b30a8fcc26228e040 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_020c26b3d02a824b42434fa1fef912c4 = $(`<div id="html_020c26b3d02a824b42434fa1fef912c4" style="width: 100.0%; height: 100.0%;">West Hub 15km Coverage</div>`)[0];
                popup_1394688be6a4b88b30a8fcc26228e040.setContent(html_020c26b3d02a824b42434fa1fef912c4);
            
        

        circle_11bc7616ec34f49dbb406a2cdbae6aee.bindPopup(popup_1394688be6a4b88b30a8fcc26228e040)
        ;

        
    
    
            var marker_09507eda5be8cf4264ffd84b67d6fa72 = L.marker(
                [24.81532, 46.78365],
                {
}
            ).addTo(map_916ed6245fdeec780df9bb7e80da6a82);
        
    
            var icon_d58f7af7c5b51441dcffddd5b8964a73 = L.AwesomeMarkers.icon(
                {
  "markerColor": "lightblue",
  "iconColor": "white",
  "icon": "building-o",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_3cd7765503813e175e12ce2e6e4514ae = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_6f3ae07622f164ef6d4c24b7af656299 = $(`<div id="html_6f3ae07622f164ef6d4c24b7af656299" style="width: 100.0%; height: 100.0%;">North Hub (Proposed)</div>`)[0];
                popup_3cd7765503813e175e12ce2e6e4514ae.setContent(html_6f3ae07622f164ef6d4c24b7af656299);
            
        

        marker_09507eda5be8cf4264ffd84b67d6fa72.bindPopup(popup_3cd7765503813e175e12ce2e6e4514ae)
        ;

        
    
    
                marker_09507eda5be8cf4264ffd84b67d6fa72.setIcon(icon_d58f7af7c5b51441dcffddd5b8964a73);
            
    
            var circle_6442c56719b8c9daa783ab616c046a9f = L.circle(
                [24.81532, 46.78365],
                {"bubblingMouseEvents": true, "color": "blue", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.1, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 15000, "stroke": true, "weight": 3}
            ).addTo(map_916ed6245fdeec780df9bb7e80da6a82);
        
    
        var popup_4a37b59b67750f78d75201acbf8ba654 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_b79f75ef476d7b4e99d988ef9b5f4820 = $(`<div id="html_b79f75ef476d7b4e99d988ef9b5f4820" style="width: 100.0%; height: 100.0%;">North Hub 15km Coverage</div>`)[0];
                popup_4a37b59b67750f78d75201acbf8ba654.setContent(html_b79f75ef476d7b4e99d988ef9b5f4820);
            
        

        circle_6442c56719b8c9daa783ab616c046a9f.bindPopup(popup_4a37b59b67750f78d75201acbf8ba654)
        ;

        
    
    
            var marker_d78d07e2b5a4fec25de410640f4afcf6 = L.marker(
                [24.57891, 46.82013],
                {
}
            ).addTo(map_916ed6245fdeec780df9bb7e80da6a82);
        
    
            var icon_ea091f91fd6e7a77ed3cb11c51443707 = L.AwesomeMarkers.icon(
                {
  "markerColor": "lightblue",
  "iconColor": "white",
  "icon": "building-o",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_0ca9a594e4a0890c637417f8327357e9 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_eb9066ddb5e87da00e3150c4cc9e127f = $(`<div id="html_eb9066ddb5e87da00e3150c4cc9e127f" style="width: 100.0%; height: 100.0%;">South Hub (Proposed)</div>`)[0];
                popup_0ca9a594e4a0890c637417f8327357e9.setContent(html_eb9066ddb5e87da00e3150c4cc9e127f);
            
        

        marker_d78d07e2b5a4fec25de410640f4afcf6.bindPopup(popup_0ca9a594e4a0890c637417f8327357e9)
        ;

        
    
    
                marker_d78d07e2b5a4fec25de410640f4afcf6.setIcon(icon_ea091f91fd6e7a77ed3cb11c51443707);
            
    
            var circle_db54e0aa40d2a9acc8f7039430ea293b = L.circle(
                [24.57891, 46.82013],
                {"bubblingMouseEvents": true, "color": "blue", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.1, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 15000, "stroke": true, "weight": 3}
            ).addTo(map_916ed6245fdeec780df9bb7e80da6a82);
        
    
        var popup_340ccf8ca5e5294ab4e364992e626fb4 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_141cda8e355733972161fa32f09db6a1 = $(`<div id="html_141cda8e355733972161fa32f09db6a1" style="width: 100.0%; height: 100.0%;">South Hub 15km Coverage</div>`)[0];
                popup_340ccf8ca5e5294ab4e364992e626fb4.setContent(html_141cda8e355733972161fa32f09db6a1);
            
        

        circle_db54e0aa40d2a9acc8f7039430ea293b.bindPopup(popup_340ccf8ca5e5294ab4e364992e626fb4)
        ;

        
    
    
            var marker_41b65524c7b709247c5a37b2472b72c7 = L.marker(
                [26.48649904976244, 50.030582320580145],
                {
}
            ).addTo(map_916ed6245fdeec780df9bb7e80da6a82);
        
    
            var icon_8463fc5429b2d59cab97158c1ec08963 = L.AwesomeMarkers.icon(
                {
  "markerColor": "green",
  "iconColor": "white",
  "icon": "crosshairs",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_da2c40bdb3e5501fd392d30e211d0e8e = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_d640cb9c51b5fdfeb65d880696d4d037 = $(`<div id="html_d640cb9c51b5fdfeb65d880696d4d037" style="width: 100.0%; height: 100.0%;">Optimal Location 1.0<br>3970.0 orders (15.1%)</div>`)[0];
                popup_da2c40bdb3e5501fd392d30e211d0e8e.setContent(html_d640cb9c51b5fdfeb65d880696d4d037);
            
        

        marker_41b65524c7b709247c5a37b2472b72c7.bindPopup(popup_da2c40bdb3e5501fd392d30e211d0e8e)
        ;

        
    
    
                marker_41b65524c7b709247c5a37b2472b72c7.setIcon(icon_8463fc5429b2d59cab97158c1ec08963);
            
    
            var circle_9a72964a68880953ccc98a4276055c49 = L.circle(
                [26.48649904976244, 50.030582320580145],
                {"bubblingMouseEvents": true, "color": "green", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.1, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 15000, "stroke": true, "weight": 3}
            ).addTo(map_916ed6245fdeec780df9bb7e80da6a82);
        
    
        var popup_23a789ed7055162da3627c1a8c4fdc24 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_6a343b8a19f513c4fe436d3f1cf7b202 = $(`<div id="html_6a343b8a19f513c4fe436d3f1cf7b202" style="width: 100.0%; height: 100.0%;">Optimal Location 1.0 15km Coverage</div>`)[0];
                popup_23a789ed7055162da3627c1a8c4fdc24.setContent(html_6a343b8a19f513c4fe436d3f1cf7b202);
            
        

        circle_9a72964a68880953ccc98a4276055c49.bindPopup(popup_23a789ed7055162da3627c1a8c4fdc24)
        ;

        
    
    
            var marker_db1b9a82f3f81d9a01780d39131c110c = L.marker(
                [24.714753411268685, 46.74768876964354],
                {
}
            ).addTo(map_916ed6245fdeec780df9bb7e80da6a82);
        
    
            var icon_68803b522c82e0f550e548b73f973a26 = L.AwesomeMarkers.icon(
                {
  "markerColor": "green",
  "iconColor": "white",
  "icon": "crosshairs",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_abd130e6000e693fefb4199a037b36e7 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_925bb85dfdd135f4fd5eee3cc530daed = $(`<div id="html_925bb85dfdd135f4fd5eee3cc530daed" style="width: 100.0%; height: 100.0%;">Optimal Location 2.0<br>5225.0 orders (19.9%)</div>`)[0];
                popup_abd130e6000e693fefb4199a037b36e7.setContent(html_925bb85dfdd135f4fd5eee3cc530daed);
            
        

        marker_db1b9a82f3f81d9a01780d39131c110c.bindPopup(popup_abd130e6000e693fefb4199a037b36e7)
        ;

        
    
    
                marker_db1b9a82f3f81d9a01780d39131c110c.setIcon(icon_68803b522c82e0f550e548b73f973a26);
            
    
            var circle_709345f2ab4e1c3bd309689ccd0079bb = L.circle(
                [24.714753411268685, 46.74768876964354],
                {"bubblingMouseEvents": true, "color": "green", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.1, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 15000, "stroke": true, "weight": 3}
            ).addTo(map_916ed6245fdeec780df9bb7e80da6a82);
        
    
        var popup_fd6ec85fbad2c3ce85268e673d4c1b87 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_b2ae967f5ba00808a43a8ac5542fbbae = $(`<div id="html_b2ae967f5ba00808a43a8ac5542fbbae" style="width: 100.0%; height: 100.0%;">Optimal Location 2.0 15km Coverage</div>`)[0];
                popup_fd6ec85fbad2c3ce85268e673d4c1b87.setContent(html_b2ae967f5ba00808a43a8ac5542fbbae);
            
        

        circle_709345f2ab4e1c3bd309689ccd0079bb.bindPopup(popup_fd6ec85fbad2c3ce85268e673d4c1b87)
        ;

        
    
    
            var marker_fc9533b932ba54e5e9839d3a754a0ac7 = L.marker(
                [24.578242969690336, 46.67072681912682],
                {
}
            ).addTo(map_916ed6245fdeec780df9bb7e80da6a82);
        
    
            var icon_a1cceb6bb982d275e67e7fd3657793c4 = L.AwesomeMarkers.icon(
                {
  "markerColor": "green",
  "iconColor": "white",
  "icon": "crosshairs",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_55c38625b88deeb94935cf2cc3e283ea = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_15e41da15393d7b921c8c54e51ec5b86 = $(`<div id="html_15e41da15393d7b921c8c54e51ec5b86" style="width: 100.0%; height: 100.0%;">Optimal Location 3.0<br>9141.0 orders (34.8%)</div>`)[0];
                popup_55c38625b88deeb94935cf2cc3e283ea.setContent(html_15e41da15393d7b921c8c54e51ec5b86);
            
        

        marker_fc9533b932ba54e5e9839d3a754a0ac7.bindPopup(popup_55c38625b88deeb94935cf2cc3e283ea)
        ;

        
    
    
                marker_fc9533b932ba54e5e9839d3a754a0ac7.setIcon(icon_a1cceb6bb982d275e67e7fd3657793c4);
            
    
            var circle_2db970b68b906344a480ec79af9d486e = L.circle(
                [24.578242969690336, 46.67072681912682],
                {"bubblingMouseEvents": true, "color": "green", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.1, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 15000, "stroke": true, "weight": 3}
            ).addTo(map_916ed6245fdeec780df9bb7e80da6a82);
        
    
        var popup_ba69504431a8669ab2daca4dbab57189 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_580c6fed74dfb556ff5ca710fb37d296 = $(`<div id="html_580c6fed74dfb556ff5ca710fb37d296" style="width: 100.0%; height: 100.0%;">Optimal Location 3.0 15km Coverage</div>`)[0];
                popup_ba69504431a8669ab2daca4dbab57189.setContent(html_580c6fed74dfb556ff5ca710fb37d296);
            
        

        circle_2db970b68b906344a480ec79af9d486e.bindPopup(popup_ba69504431a8669ab2daca4dbab57189)
        ;

        
    
    
            var marker_054e823697b89b7df4b29b66fc044eaf = L.marker(
                [24.81628187352046, 46.72597252282719],
                {
}
            ).addTo(map_916ed6245fdeec780df9bb7e80da6a82);
        
    
            var icon_a1fb1d79e83a6be2606157b8e5d31879 = L.AwesomeMarkers.icon(
                {
  "markerColor": "green",
  "iconColor": "white",
  "icon": "crosshairs",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_6d43593c818dd787a4a3d5ff27fa2e8f = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_657b6602246dbc540292916b8a476f48 = $(`<div id="html_657b6602246dbc540292916b8a476f48" style="width: 100.0%; height: 100.0%;">Optimal Location 4.0<br>5905.0 orders (22.5%)</div>`)[0];
                popup_6d43593c818dd787a4a3d5ff27fa2e8f.setContent(html_657b6602246dbc540292916b8a476f48);
            
        

        marker_054e823697b89b7df4b29b66fc044eaf.bindPopup(popup_6d43593c818dd787a4a3d5ff27fa2e8f)
        ;

        
    
    
                marker_054e823697b89b7df4b29b66fc044eaf.setIcon(icon_a1fb1d79e83a6be2606157b8e5d31879);
            
    
            var circle_a8c8fc7c99ad87de2870089be924e688 = L.circle(
                [24.81628187352046, 46.72597252282719],
                {"bubblingMouseEvents": true, "color": "green", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.1, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 15000, "stroke": true, "weight": 3}
            ).addTo(map_916ed6245fdeec780df9bb7e80da6a82);
        
    
        var popup_a7f3a8061c1954a5eabdb8a11b76840c = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_7093f8dffb540c3d8f432115cb070fee = $(`<div id="html_7093f8dffb540c3d8f432115cb070fee" style="width: 100.0%; height: 100.0%;">Optimal Location 4.0 15km Coverage</div>`)[0];
                popup_a7f3a8061c1954a5eabdb8a11b76840c.setContent(html_7093f8dffb540c3d8f432115cb070fee);
            
        

        circle_a8c8fc7c99ad87de2870089be924e688.bindPopup(popup_a7f3a8061c1954a5eabdb8a11b76840c)
        ;

        
    
    
            var marker_87095030bf4ed03e69c67da5cc542436 = L.marker(
                [26.291071442835747, 50.128648477284074],
                {
}
            ).addTo(map_916ed6245fdeec780df9bb7e80da6a82);
        
    
            var icon_25c2033214208bad738a2f6b3e5bb904 = L.AwesomeMarkers.icon(
                {
  "markerColor": "green",
  "iconColor": "white",
  "icon": "crosshairs",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_f050526437ce92fe943c6b905dc06d56 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_95c77a77b8e852f39b51c97ef38bb0fe = $(`<div id="html_95c77a77b8e852f39b51c97ef38bb0fe" style="width: 100.0%; height: 100.0%;">Optimal Location 5.0<br>2032.0 orders (7.7%)</div>`)[0];
                popup_f050526437ce92fe943c6b905dc06d56.setContent(html_95c77a77b8e852f39b51c97ef38bb0fe);
            
        

        marker_87095030bf4ed03e69c67da5cc542436.bindPopup(popup_f050526437ce92fe943c6b905dc06d56)
        ;

        
    
    
                marker_87095030bf4ed03e69c67da5cc542436.setIcon(icon_25c2033214208bad738a2f6b3e5bb904);
            
    
            var circle_e2f52c9168cfa60735b3b227a84aca44 = L.circle(
                [26.291071442835747, 50.128648477284074],
                {"bubblingMouseEvents": true, "color": "green", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.1, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 15000, "stroke": true, "weight": 3}
            ).addTo(map_916ed6245fdeec780df9bb7e80da6a82);
        
    
        var popup_3d3eb02f516aa81f68f9e6abc5e03880 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_e9e5ce2a1c5a91189047c6670044749f = $(`<div id="html_e9e5ce2a1c5a91189047c6670044749f" style="width: 100.0%; height: 100.0%;">Optimal Location 5.0 15km Coverage</div>`)[0];
                popup_3d3eb02f516aa81f68f9e6abc5e03880.setContent(html_e9e5ce2a1c5a91189047c6670044749f);
            
        

        circle_e2f52c9168cfa60735b3b227a84aca44.bindPopup(popup_3d3eb02f516aa81f68f9e6abc5e03880)
        ;

        
    
</script>
</html>