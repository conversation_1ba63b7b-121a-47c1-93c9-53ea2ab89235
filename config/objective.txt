Okay, here's a detailed prompt explicitly designed to guide an AI within an IDE environment to perform data analysis on the pharmacy order log, emphasizing organization and documentation. This is helpful because some AI assistants can interact directly with your code editor.

Generated code
### **AI IDE Assistant Prompt: Data Analysis for Pharmacy Delivery Optimization**

**1. Task Overview:**

You are an AI assistant within a data science IDE (e.g., Jupyter Notebook, VS Code).  Your primary goal is to analyze a pharmacy order dataset and provide insights to the operations team for optimizing resource allocation and delivery efficiency. You are expected to write well-documented, easily understandable Python code within this IDE, producing visualizations to support your findings, and providing prioritized, actionable recommendations.

**2. Data Input:**

*   **Data Format:** The input will be a CSV-formatted string provided as a variable.
*   **Data Structure:** Each row represents a pharmacy order and will be available in a Pandas DataFrame.
*   **Columns:** The CSV data will have the following columns:
    *   `timestamp`: Date and time the order was placed (string, convert to datetime).
    *   `branch_license`: Identifier for the pharmacy branch.
    *   `delivery_date`: Date delivery is scheduled (string, convert to datetime).
    *   `delivery_time_slot_end_time`: End time of delivery slot (string - e.g., "13:00:00", treat as HH:MM:SS).
    *   `delivery_time_slot_start_time`: Start time of delivery slot (string - e.g., "11:00:00", treat as HH:MM:SS).
    *   `erx_reference`: Prescription reference (string).
    *   `is_pickup`: Boolean indicator if the order is a pickup (True/False, interpret as boolean).
    *   `order_id`: Unique order identifier (string).
    *   `first_name`: Customer's first name (string).
    *   `gender`: Customer's gender (string).
    *   `last_name`: Customer's last name (string).
    *   `member_id`: Customer's member ID (numerical or string).
    *   `national_id`: Customer's national ID (numerical or string).
    *   `area`: Delivery area name (string).
    *   `latitude`: Latitude coordinate of the delivery location (numerical).
    *   `longitude`: Longitude coordinate of the delivery location (numerical).

**3. Detailed Steps and Code Generation Instructions:**

1.  **Import Libraries:** Start with importing essential Python libraries: `pandas`, `numpy`, `matplotlib.pyplot`, `seaborn`, `folium`. Add a code cell for this.
     ```python
        import pandas as pd
        import numpy as np
        import matplotlib.pyplot as plt
        import seaborn as sns
        import folium
        from folium.plugins import HeatMap, MarkerCluster
     ```

2.  **Load and Inspect Data:** Create a pandas DataFrame from the CSV string. Use `pd.read_csv(io.StringIO(csv_data))` to import the data. Then, print the first 5 rows with `df.head()` and display general information about the DataFrame (data types, non-null counts) using `df.info()`.

3.  **Data Cleaning:**
    *   Convert relevant columns to correct data types:
        *   `timestamp` and `delivery_date` to `datetime` objects.
        *   `delivery_time_slot_start_time` and `delivery_time_slot_end_time` to `datetime.time` objects. Handle potential errors if time strings are malformed.
        *   `is_pickup` to boolean
        ```python
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df['delivery_date'] = pd.to_datetime(df['delivery_date'])
        df['delivery_time_slot_start_time'] = pd.to_datetime(df['delivery_time_slot_start_time'], format='%H:%M:%S', errors='coerce').dt.time
        df['delivery_time_slot_end_time'] = pd.to_datetime(df['delivery_time_slot_end_time'], format='%H:%M:%S', errors='coerce').dt.time
        df['is_pickup'] = df['is_pickup'].astype(bool)
        ```
    *   Clean the `area` column by stripping leading/trailing spaces and removing punctuation.
    *   Handle missing values (NaNs). Drop rows where `delivery_date`, `latitude`, or `longitude` is missing.
     ```python
        df['area'] = df['area'].str.strip().str.replace('،', '') # clean area names
        df.dropna(subset=['delivery_date', 'latitude', 'longitude'], inplace=True) # Drop nan values
     ```

4.  **Feature Engineering:** Create new columns to simplify analysis:
    *   `delivery_day_of_week`: Extract the day of the week from `delivery_date`.
    *   `delivery_hour_start`: Extract the start hour (0-23) from the `delivery_time_slot_start_time`.
     ```python
        df['delivery_day_of_week'] = df['delivery_date'].dt.day_name() # create day column
        df['delivery_hour_start'] = pd.to_datetime(df['delivery_time_slot_start_time'].astype(str), format='%H:%M:%S', errors='coerce').dt.hour # Delivery time starts column
     ```

5.  **Temporal Analysis:** Generate charts to visualize order distribution:
    *   **Deliveries by Day of the Week:** Use `seaborn.countplot` to show order counts by day of the week. Order weekdays logically (Monday to Sunday). Include axis labels and a title.

    *   **Deliveries by Time Slot:** Use `seaborn.countplot` to show order counts by delivery time slot. Sort the time slots chronologically based on the start hour. Include labels and a title.

    *   **Order Placement Time Analysis:** Plot the hourly distribution of order placement times using `matplotlib.pyplot.hist`. Compare this against delivery start times. Add titles and axis labels.
    ```python
        #Example Code for creating a chart
        plt.figure(figsize=(12, 6))
        sns.countplot(data=df, x='delivery_day_of_week', order=day_order, palette='viridis')
        plt.title('Total Deliveries by Day of the Week', fontsize=16)
        plt.xlabel('Day of the Week', fontsize=12)
        plt.ylabel('Number of Orders', fontsize=12)
        plt.show()
    ```

6.  **Geospatial Analysis:** Create geographical visualizations:

    *   **Top Delivery Areas:** Use `seaborn.barplot` to show the N most common delivery areas.
        ```python
        top_areas = df['area'].value_counts().nlargest(15)

        plt.figure(figsize=(12, 8))
        sns.barplot(y=top_areas.index, x=top_areas.values, palette='mako')
        plt.title('Top 15 Busiest Delivery Areas', fontsize=16)
        plt.xlabel('Number of Orders', fontsize=12)
        plt.ylabel('Area', fontsize=12)
        plt.show()
        ```

    *   **Heatmap (Folium):**
        *   Create a Folium map centered around the average latitude/longitude of the data.
        *   Use Folium's `HeatMap` plugin to generate a heatmap showing order density. Customize the heatmap radius for optimal visualization.
        *   Save the heatmap to an HTML file for easy viewing.
        ```python
        map_center = [df['latitude'].mean(), df['longitude'].mean()]
        heatmap = folium.Map(location=map_center, zoom_start=10)

        heat_data = [[row['latitude'], row['longitude']] for index, row in df.iterrows()]

        HeatMap(heat_data, radius=15).add_to(heatmap)

        heatmap.save('delivery_heatmap.html')
        ```

    *   **Cluster Map (Folium):**
        *   Create a Folium map with a `MarkerCluster` layer.
        *   Add a marker for each order, displaying area/time slot information in a popup.
        *   Save the cluster map to an HTML file.

7.  **Branch Performance Analysis:**

    *   Determine the number of orders fulfilled by each `branch_license`.
    *   Create a bar chart showing the busiest 20 branches.
       ```python
       plt.figure(figsize=(14, 7))
        branch_counts = df['branch_license'].value_counts().nlargest(20)
        sns.barplot(x=branch_counts.values, y=branch_counts.index, palette='crest')
        plt.title('Top 20 Busiest Branches by Order Volume', fontsize=16)
        plt.xlabel('Number of Orders', fontsize=12)
        plt.ylabel('Branch License', fontsize=12)
        plt.show()
       ```

8.  **Actionable Insights and Recommendations:**
    *   Summarize your findings in clear, concise bullet points.
    *   Provide at least five specific, actionable, and *prioritized* recommendations.
    *   For each recommendation, explain:
        *   The basis for the recommendation (reference specific analysis points).
        *   The expected impact (e.g., efficiency improvement, cost savings, improved customer service).
    *   If possible, quantify the impact (e.g., "Reduce delivery times by 15% by better allocating drivers").

9.  **Documentation:**

    *   Each code cell should have a brief comment explaining its purpose.
    *   Before each visualization, include a short descriptive comment explaining what the graph illustrates and why it's relevant to the overall analysis.
    *   After the code, present your findings in a markdown cell that summarizes the implications of the analysis.

**4. Evaluation Criteria:**

*   **Code Quality:** Code should be well-structured, readable, and properly documented.
*   **Correctness:** Analysis should accurately reflect the data.
*   **Completeness:**  All requested analysis steps should be addressed.
*   **Actionability:** Recommendations should be clear, practical, and useful for the operations team.

**5. Provided Input Data (CSV Format):**


PASTE YOUR CSV DATA HERE (From the previous response)

Generated code
This prompt provides a much more structured guide for an AI assistant inside an IDE, emphasizing not only the analysis but also the need for well-documented and presentable code and actionable recommendations.
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END