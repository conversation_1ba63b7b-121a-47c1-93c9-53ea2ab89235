{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# <PERSON><PERSON><PERSON> Leads Analytics Dashboard\n", "\n", "Comprehensive analytics for Wasfaty pharmacy delivery orders including:\n", "- Order volume trends and patterns\n", "- Geographic distribution analysis\n", "- Day of week and time preferences\n", "- Branch performance analysis\n", "- Customer demographics insights\n", "- Operational recommendations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Import custom utilities\n", "from data_utils import clean_merged_logs, get_time_slot_stats, get_area_stats, get_branch_stats\n", "\n", "# Set style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "# Display settings\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "\n", "print(\"📊 Wasfaty Analytics - Libraries loaded successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Data Loading and Overview"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load and clean the data\n", "df = clean_merged_logs('daily_logs/merged_logs.csv')\n", "\n", "print(f\"📈 Dataset Overview:\")\n", "print(f\"Total orders: {len(df):,}\")\n", "print(f\"Date range: {df['date'].min()} to {df['date'].max()}\")\n", "print(f\"Unique branches: {df['branch_license'].nunique()}\")\n", "print(f\"Unique areas: {df['area_clean'].nunique()}\")\n", "print(f\"Orders with coordinates: {df[['latitude', 'longitude']].notna().all(axis=1).sum():,}\")\n", "\n", "# Display sample data\n", "print(\"\\n📋 Sample data:\")\n", "df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Order Volume Trends"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Daily order trends\n", "daily_orders = df.groupby('date').size().reset_index(name='order_count')\n", "daily_orders['date'] = pd.to_datetime(daily_orders['date'])\n", "\n", "# Create interactive plot\n", "fig = px.line(daily_orders, x='date', y='order_count', \n", "              title='📈 Daily Order Volume Trend',\n", "              labels={'order_count': 'Number of Orders', 'date': 'Date'})\n", "fig.update_layout(height=500)\n", "fig.show()\n", "\n", "# Summary statistics\n", "print(f\"📊 Daily Order Statistics:\")\n", "print(f\"Average daily orders: {daily_orders['order_count'].mean():.1f}\")\n", "print(f\"Peak day: {daily_orders['order_count'].max():,} orders on {daily_orders.loc[daily_orders['order_count'].idxmax(), 'date'].strftime('%Y-%m-%d')}\")\n", "print(f\"Minimum day: {daily_orders['order_count'].min():,} orders on {daily_orders.loc[daily_orders['order_count'].idxmin(), 'date'].strftime('%Y-%m-%d')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Day of Week Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Day of week analysis\n", "day_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']\n", "dow_orders = df.groupby('day_of_week').size().reindex(day_order)\n", "\n", "# Create visualization\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# Bar chart\n", "dow_orders.plot(kind='bar', ax=ax1, color='skyblue')\n", "ax1.set_title('📅 Orders by Day of Week', fontsize=14, fontweight='bold')\n", "ax1.set_xlabel('Day of Week')\n", "ax1.set_ylabel('Number of Orders')\n", "ax1.tick_params(axis='x', rotation=45)\n", "\n", "# Pie chart\n", "dow_orders.plot(kind='pie', ax=ax2, autopct='%1.1f%%', startangle=90)\n", "ax2.set_title('📊 Day of Week Distribution', fontsize=14, fontweight='bold')\n", "ax2.set_ylabel('')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Print insights\n", "busiest_day = dow_orders.idxmax()\n", "quietest_day = dow_orders.idxmin()\n", "weekend_orders = dow_orders[['Saturday', 'Sunday']].sum()\n", "weekday_orders = dow_orders[['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']].sum()\n", "\n", "print(f\"🔥 Busiest day: {busiest_day} ({dow_orders[busiest_day]:,} orders)\")\n", "print(f\"😴 Quietest day: {quietest_day} ({dow_orders[quietest_day]:,} orders)\")\n", "print(f\"📊 Weekend vs Weekday ratio: {weekend_orders/weekday_orders:.2f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Hourly order patterns\n", "hourly_orders = df.groupby('hour').size()\n", "\n", "# Create heatmap for hour vs day of week\n", "hour_dow_pivot = df.groupby(['hour', 'day_of_week']).size().unstack(fill_value=0)\n", "hour_dow_pivot = hour_dow_pivot.reindex(columns=day_order)\n", "\n", "# Plot hourly patterns\n", "fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))\n", "\n", "# Hourly distribution\n", "hourly_orders.plot(kind='bar', ax=ax1, color='lightcoral')\n", "ax1.set_title('🕐 Orders by Hour of Day', fontsize=16, fontweight='bold')\n", "ax1.set_xlabel('Hour of Day')\n", "ax1.set_ylabel('Number of Orders')\n", "ax1.tick_params(axis='x', rotation=0)\n", "\n", "# Heatmap\n", "sns.heatmap(hour_dow_pivot.T, annot=True, fmt='d', cmap='YlOrRd', ax=ax2)\n", "ax2.set_title('🔥 Order Heatmap: Hour vs Day of Week', fontsize=16, fontweight='bold')\n", "ax2.set_xlabel('Hour of Day')\n", "ax2.set_ylabel('Day of Week')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Peak hours analysis\n", "peak_hours = hourly_orders.nlargest(3)\n", "print(f\"🔥 Peak hours:\")\n", "for hour, count in peak_hours.items():\n", "    print(f\"  {hour:02d}:00 - {count:,} orders\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Geographic Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Area analysis\n", "area_stats = get_area_stats(df)\n", "top_areas = area_stats.head(20)\n", "\n", "# Create interactive bar chart\n", "fig = px.bar(top_areas.reset_index(), \n", "             x='area_clean', y='order_count',\n", "             title='📍 Top 20 Areas by Order Volume',\n", "             labels={'order_count': 'Number of Orders', 'area_clean': 'Area'})\n", "fig.update_layout(height=600, xaxis_tickangle=-45)\n", "fig.show()\n", "\n", "print(f\"📊 Geographic Distribution Summary:\")\n", "print(f\"Top 5 areas account for {top_areas.head(5)['order_percentage'].sum():.1f}% of all orders\")\n", "print(f\"\\n🏆 Top 10 Areas:\")\n", "for i, (area, stats) in enumerate(top_areas.head(10).iterrows(), 1):\n", "    print(f\"  {i:2d}. {area}: {stats['order_count']:,} orders ({stats['order_percentage']:.1f}%)\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}