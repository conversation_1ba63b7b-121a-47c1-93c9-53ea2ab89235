<!DOCTYPE html>
<html>
<head>
    
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script src="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="https://netdna.bootstrapcdn.com/bootstrap/3.0.0/css/bootstrap-glyphicons.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.2.0/css/all.min.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/python-visualization/folium/folium/templates/leaflet.awesome.rotate.min.css"/>
    
            <meta name="viewport" content="width=device-width,
                initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
            <style>
                #map_c9740a3e5c262bf5eaf15ae69f13c876 {
                    position: relative;
                    width: 100.0%;
                    height: 100.0%;
                    left: 0.0%;
                    top: 0.0%;
                }
                .leaflet-container { font-size: 1rem; }
            </style>

            <style>html, body {
                width: 100%;
                height: 100%;
                margin: 0;
                padding: 0;
            }
            </style>

            <style>#map {
                position:absolute;
                top:0;
                bottom:0;
                right:0;
                left:0;
                }
            </style>

            <script>
                L_NO_TOUCH = false;
                L_DISABLE_3D = false;
            </script>

        
</head>
<body>
    
    
            <div class="folium-map" id="map_c9740a3e5c262bf5eaf15ae69f13c876" ></div>
        
</body>
<script>
    
    
            var map_c9740a3e5c262bf5eaf15ae69f13c876 = L.map(
                "map_c9740a3e5c262bf5eaf15ae69f13c876",
                {
                    center: [24.68769, 46.80032],
                    crs: L.CRS.EPSG3857,
                    ...{
  "zoom": 10,
  "zoomControl": true,
  "preferCanvas": false,
}

                }
            );

            

        
    
            var tile_layer_51ab93b0c0a9f321e5bc1a25223144ee = L.tileLayer(
                "https://tile.openstreetmap.org/{z}/{x}/{y}.png",
                {
  "minZoom": 0,
  "maxZoom": 19,
  "maxNativeZoom": 19,
  "noWrap": false,
  "attribution": "\u0026copy; \u003ca href=\"https://www.openstreetmap.org/copyright\"\u003eOpenStreetMap\u003c/a\u003e contributors",
  "subdomains": "abc",
  "detectRetina": false,
  "tms": false,
  "opacity": 1,
}

            );
        
    
            tile_layer_51ab93b0c0a9f321e5bc1a25223144ee.addTo(map_c9740a3e5c262bf5eaf15ae69f13c876);
        
    
            var marker_5b5fb06094e5a5bcfd70c99fae21ba34 = L.marker(
                [24.68769, 46.80032],
                {
}
            ).addTo(map_c9740a3e5c262bf5eaf15ae69f13c876);
        
    
            var icon_b0f0a4e3d933c4e6d594db821d588038 = L.AwesomeMarkers.icon(
                {
  "markerColor": "black",
  "iconColor": "white",
  "icon": "star",
  "prefix": "glyphicon",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_04a3905a1a7161d05b493d604e6ba402 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_45514f69b298defce29363af259f87fe = $(`<div id="html_45514f69b298defce29363af259f87fe" style="width: 100.0%; height: 100.0%;">Central Hub / Dark Store</div>`)[0];
                popup_04a3905a1a7161d05b493d604e6ba402.setContent(html_45514f69b298defce29363af259f87fe);
            
        

        marker_5b5fb06094e5a5bcfd70c99fae21ba34.bindPopup(popup_04a3905a1a7161d05b493d604e6ba402)
        ;

        
    
    
                marker_5b5fb06094e5a5bcfd70c99fae21ba34.setIcon(icon_b0f0a4e3d933c4e6d594db821d588038);
            
    
            var circle_05b3a624bfe73f79a8b36fee6a503c02 = L.circle(
                [24.68769, 46.80032],
                {"bubblingMouseEvents": true, "color": "#2ca02c", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#2ca02c", "fillOpacity": 0.1, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5000, "stroke": true, "weight": 3}
            ).addTo(map_c9740a3e5c262bf5eaf15ae69f13c876);
        
    
        var popup_1b791da997e57b23141335b511197fc2 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_af85d8bdc6fa63827e662e9fe649887a = $(`<div id="html_af85d8bdc6fa63827e662e9fe649887a" style="width: 100.0%; height: 100.0%;">Tier 1 (≤ 5km)</div>`)[0];
                popup_1b791da997e57b23141335b511197fc2.setContent(html_af85d8bdc6fa63827e662e9fe649887a);
            
        

        circle_05b3a624bfe73f79a8b36fee6a503c02.bindPopup(popup_1b791da997e57b23141335b511197fc2)
        ;

        
    
    
            var circle_464953d592cbb5c9c8c8a9ed0206b3ae = L.circle(
                [24.68769, 46.80032],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.05, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 15000, "stroke": true, "weight": 3}
            ).addTo(map_c9740a3e5c262bf5eaf15ae69f13c876);
        
    
        var popup_680b03620c806e0cfa8e169f154f8037 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_41bc633333302879163ad4f9712cef18 = $(`<div id="html_41bc633333302879163ad4f9712cef18" style="width: 100.0%; height: 100.0%;">Tier 2 (≤ 15km)</div>`)[0];
                popup_680b03620c806e0cfa8e169f154f8037.setContent(html_41bc633333302879163ad4f9712cef18);
            
        

        circle_464953d592cbb5c9c8c8a9ed0206b3ae.bindPopup(popup_680b03620c806e0cfa8e169f154f8037)
        ;

        
    
    
            var feature_group_a7e59382f86081ceb863282d40faf6e1 = L.featureGroup(
                {
}
            );
        
    
            var circle_marker_293832ae930a8816978f3615a770c91a = L.circleMarker(
                [24.7037875, 46.817255172413795],
                {"bubblingMouseEvents": true, "color": "#2ca02c", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#2ca02c", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_a7e59382f86081ceb863282d40faf6e1);
        
    
        var popup_5a863b2425861badf916f0192677ce96 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_8c738f0f1e89e1bcf7c97b256d9167df = $(`<div id="html_8c738f0f1e89e1bcf7c97b256d9167df" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY30<br>             <b>Area:</b> حي السلام<br>             <b>Orders:</b> 232<br>             <b>Distance:</b> 2.5 km<br>             <b>Tier:</b> Tier 1             </div>`)[0];
                popup_5a863b2425861badf916f0192677ce96.setContent(html_8c738f0f1e89e1bcf7c97b256d9167df);
            
        

        circle_marker_293832ae930a8816978f3615a770c91a.bindPopup(popup_5a863b2425861badf916f0192677ce96)
        ;

        
    
    
            var circle_marker_6439c7e50dc798184591ca63d6e4aa2c = L.circleMarker(
                [24.71692, 46.77309304347826],
                {"bubblingMouseEvents": true, "color": "#2ca02c", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#2ca02c", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_a7e59382f86081ceb863282d40faf6e1);
        
    
        var popup_46e3c24e605f5794fe02a6a6e224feb2 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_66e3b93d8eee8c690660e059090264c4 = $(`<div id="html_66e3b93d8eee8c690660e059090264c4" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY31<br>             <b>Area:</b> حي الريان<br>             <b>Orders:</b> 115<br>             <b>Distance:</b> 4.3 km<br>             <b>Tier:</b> Tier 1             </div>`)[0];
                popup_46e3c24e605f5794fe02a6a6e224feb2.setContent(html_66e3b93d8eee8c690660e059090264c4);
            
        

        circle_marker_6439c7e50dc798184591ca63d6e4aa2c.bindPopup(popup_46e3c24e605f5794fe02a6a6e224feb2)
        ;

        
    
    
            var circle_marker_f378ddea777a760d8e8916ea2d5b8df7 = L.circleMarker(
                [24.68724807692308, 46.79436826923077],
                {"bubblingMouseEvents": true, "color": "#2ca02c", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#2ca02c", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_a7e59382f86081ceb863282d40faf6e1);
        
    
        var popup_232505deeb8c8cb83a89d9e9c1d4cc0a = L.popup({
  "maxWidth": 200,
});

        
            
                var html_82b1d3051a1d65fea1b477f617b22504 = $(`<div id="html_82b1d3051a1d65fea1b477f617b22504" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY32<br>             <b>Area:</b> حي الروابي<br>             <b>Orders:</b> 208<br>             <b>Distance:</b> 0.6 km<br>             <b>Tier:</b> Tier 1             </div>`)[0];
                popup_232505deeb8c8cb83a89d9e9c1d4cc0a.setContent(html_82b1d3051a1d65fea1b477f617b22504);
            
        

        circle_marker_f378ddea777a760d8e8916ea2d5b8df7.bindPopup(popup_232505deeb8c8cb83a89d9e9c1d4cc0a)
        ;

        
    
    
            var circle_marker_4aafe91aca7704a491cc4d53e0e74e23 = L.circleMarker(
                [24.68412071428571, 46.81865857142858],
                {"bubblingMouseEvents": true, "color": "#2ca02c", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#2ca02c", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_a7e59382f86081ceb863282d40faf6e1);
        
    
        var popup_69059222701af04eff5536792ff19b03 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_97b9081e635bd99cc0d0c28f35d7b2e7 = $(`<div id="html_97b9081e635bd99cc0d0c28f35d7b2e7" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY33<br>             <b>Area:</b> حي الفيحاء<br>             <b>Orders:</b> 140<br>             <b>Distance:</b> 1.9 km<br>             <b>Tier:</b> Tier 1             </div>`)[0];
                popup_69059222701af04eff5536792ff19b03.setContent(html_97b9081e635bd99cc0d0c28f35d7b2e7);
            
        

        circle_marker_4aafe91aca7704a491cc4d53e0e74e23.bindPopup(popup_69059222701af04eff5536792ff19b03)
        ;

        
    
    
            var circle_marker_000270308e9307b564acc7b79fb8299f = L.circleMarker(
                [24.688001265822784, 46.76767848101266],
                {"bubblingMouseEvents": true, "color": "#2ca02c", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#2ca02c", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_a7e59382f86081ceb863282d40faf6e1);
        
    
        var popup_a22bb16c013464f31c837c1943e67340 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_aaa48cb9dc3c99c174448093c9645540 = $(`<div id="html_aaa48cb9dc3c99c174448093c9645540" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY34<br>             <b>Area:</b> Ar Rabwah<br>             <b>Orders:</b> 79<br>             <b>Distance:</b> 3.3 km<br>             <b>Tier:</b> Tier 1             </div>`)[0];
                popup_a22bb16c013464f31c837c1943e67340.setContent(html_aaa48cb9dc3c99c174448093c9645540);
            
        

        circle_marker_000270308e9307b564acc7b79fb8299f.bindPopup(popup_a22bb16c013464f31c837c1943e67340)
        ;

        
    
    
            var circle_marker_e9664e702f7a039169705bea8c851df8 = L.circleMarker(
                [24.72958251121076, 46.80097085201794],
                {"bubblingMouseEvents": true, "color": "#2ca02c", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#2ca02c", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_a7e59382f86081ceb863282d40faf6e1);
        
    
        var popup_b24f34110722958276d82c4ce43fc3dc = L.popup({
  "maxWidth": 200,
});

        
            
                var html_b63c1ebf86cac7fdfc40c169c5bf5971 = $(`<div id="html_b63c1ebf86cac7fdfc40c169c5bf5971" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY83<br>             <b>Area:</b> حي النسيم الغربي<br>             <b>Orders:</b> 223<br>             <b>Distance:</b> 4.7 km<br>             <b>Tier:</b> Tier 1             </div>`)[0];
                popup_b24f34110722958276d82c4ce43fc3dc.setContent(html_b63c1ebf86cac7fdfc40c169c5bf5971);
            
        

        circle_marker_e9664e702f7a039169705bea8c851df8.bindPopup(popup_b24f34110722958276d82c4ce43fc3dc)
        ;

        
    
    
            var circle_marker_ea7637ba5385d46a11e02c51c11248dc = L.circleMarker(
                [24.701434444444445, 46.84322888888889],
                {"bubblingMouseEvents": true, "color": "#2ca02c", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#2ca02c", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_a7e59382f86081ceb863282d40faf6e1);
        
    
        var popup_e8ecde6d9d0e80a57e10eb856a60f9d7 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_48d471325a4011df0534661dbbf261f1 = $(`<div id="html_48d471325a4011df0534661dbbf261f1" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY84<br>             <b>Area:</b> حي السعادة<br>             <b>Orders:</b> 180<br>             <b>Distance:</b> 4.6 km<br>             <b>Tier:</b> Tier 1             </div>`)[0];
                popup_e8ecde6d9d0e80a57e10eb856a60f9d7.setContent(html_48d471325a4011df0534661dbbf261f1);
            
        

        circle_marker_ea7637ba5385d46a11e02c51c11248dc.bindPopup(popup_e8ecde6d9d0e80a57e10eb856a60f9d7)
        ;

        
    
    
            var circle_marker_5569fff9cc50a69b40a124fad6181915 = L.circleMarker(
                [24.66908429319372, 46.79725916230367],
                {"bubblingMouseEvents": true, "color": "#2ca02c", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#2ca02c", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_a7e59382f86081ceb863282d40faf6e1);
        
    
        var popup_fdad3eea3aedf1e5e0f7e0a300562955 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_7c7f7feee952d392c3926f81addd3d6c = $(`<div id="html_7c7f7feee952d392c3926f81addd3d6c" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY85<br>             <b>Area:</b> حي الجزيرة<br>             <b>Orders:</b> 191<br>             <b>Distance:</b> 2.1 km<br>             <b>Tier:</b> Tier 1             </div>`)[0];
                popup_fdad3eea3aedf1e5e0f7e0a300562955.setContent(html_7c7f7feee952d392c3926f81addd3d6c);
            
        

        circle_marker_5569fff9cc50a69b40a124fad6181915.bindPopup(popup_fdad3eea3aedf1e5e0f7e0a300562955)
        ;

        
    
    
            var circle_marker_3c600eae8c7d942ec6177208c8823485 = L.circleMarker(
                [24.6633, 46.82252777777778],
                {"bubblingMouseEvents": true, "color": "#2ca02c", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#2ca02c", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_a7e59382f86081ceb863282d40faf6e1);
        
    
        var popup_70e3ff6159121594ce8e3f62125909d9 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_3dc51ba897c1b4669ced1aaf655a086e = $(`<div id="html_3dc51ba897c1b4669ced1aaf655a086e" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY86<br>             <b>Area:</b> حي الجزيرة<br>             <b>Orders:</b> 18<br>             <b>Distance:</b> 3.5 km<br>             <b>Tier:</b> Tier 1             </div>`)[0];
                popup_70e3ff6159121594ce8e3f62125909d9.setContent(html_3dc51ba897c1b4669ced1aaf655a086e);
            
        

        circle_marker_3c600eae8c7d942ec6177208c8823485.bindPopup(popup_70e3ff6159121594ce8e3f62125909d9)
        ;

        
    
    
            var circle_marker_d0538362a845e381a92ad1e7fa91317b = L.circleMarker(
                [24.657304615384614, 46.793443076923076],
                {"bubblingMouseEvents": true, "color": "#2ca02c", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#2ca02c", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_a7e59382f86081ceb863282d40faf6e1);
        
    
        var popup_fad9dc931512b386bf8ad8ea7c9995d3 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_0a59aea7b63111327f3b5c56e2fe2150 = $(`<div id="html_0a59aea7b63111327f3b5c56e2fe2150" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY95<br>             <b>Area:</b> حي الجزيرة<br>             <b>Orders:</b> 65<br>             <b>Distance:</b> 3.4 km<br>             <b>Tier:</b> Tier 1             </div>`)[0];
                popup_fad9dc931512b386bf8ad8ea7c9995d3.setContent(html_0a59aea7b63111327f3b5c56e2fe2150);
            
        

        circle_marker_d0538362a845e381a92ad1e7fa91317b.bindPopup(popup_fad9dc931512b386bf8ad8ea7c9995d3)
        ;

        
    
    
            feature_group_a7e59382f86081ceb863282d40faf6e1.addTo(map_c9740a3e5c262bf5eaf15ae69f13c876);
        
    
            var feature_group_1e0b3e902fb1c3c0fe9e02abefe19459 = L.featureGroup(
                {
}
            );
        
    
            var circle_marker_3165ca4739a2c725bacfb4ee55a43501 = L.circleMarker(
                [24.645408333333332, 46.661856944444445],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_d48061b875887b019f68e9f6335c205d = L.popup({
  "maxWidth": 200,
});

        
            
                var html_8cafd60c14f7f3a7339d36405bcc86ee = $(`<div id="html_8cafd60c14f7f3a7339d36405bcc86ee" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY100<br>             <b>Area:</b> Ar Rafiah<br>             <b>Orders:</b> 72<br>             <b>Distance:</b> 14.8 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_d48061b875887b019f68e9f6335c205d.setContent(html_8cafd60c14f7f3a7339d36405bcc86ee);
            
        

        circle_marker_3165ca4739a2c725bacfb4ee55a43501.bindPopup(popup_d48061b875887b019f68e9f6335c205d)
        ;

        
    
    
            var circle_marker_78792b450d05790da857b68288de92b7 = L.circleMarker(
                [24.635973846153846, 46.6945626923077],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_290347e5fe48d0b6705a53a340eb79ff = L.popup({
  "maxWidth": 200,
});

        
            
                var html_486fea8b8cbf1730475dbb0d92066d22 = $(`<div id="html_486fea8b8cbf1730475dbb0d92066d22" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY102<br>             <b>Area:</b> حي عليشة<br>             <b>Orders:</b> 260<br>             <b>Distance:</b> 12.1 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_290347e5fe48d0b6705a53a340eb79ff.setContent(html_486fea8b8cbf1730475dbb0d92066d22);
            
        

        circle_marker_78792b450d05790da857b68288de92b7.bindPopup(popup_290347e5fe48d0b6705a53a340eb79ff)
        ;

        
    
    
            var circle_marker_20df953cbacd8680e0b4f8d4b410985a = L.circleMarker(
                [24.562656043956046, 46.82137032967033],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_e6b83b7a939742663c1923ebe908edce = L.popup({
  "maxWidth": 200,
});

        
            
                var html_3e59ddaa8110526a4b8445a6b5654f6a = $(`<div id="html_3e59ddaa8110526a4b8445a6b5654f6a" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY104<br>             <b>Area:</b> Ad Dar Al Baida<br>             <b>Orders:</b> 91<br>             <b>Distance:</b> 14.1 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_e6b83b7a939742663c1923ebe908edce.setContent(html_3e59ddaa8110526a4b8445a6b5654f6a);
            
        

        circle_marker_20df953cbacd8680e0b4f8d4b410985a.bindPopup(popup_e6b83b7a939742663c1923ebe908edce)
        ;

        
    
    
            var circle_marker_cfd7af3b490d1707840dc5c192b2d9e1 = L.circleMarker(
                [24.575051319648097, 46.744930498533726],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_44b488cef017a24d489fae4c452593d9 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_0e2f730ad2ba9467aec002d0e62c682a = $(`<div id="html_0e2f730ad2ba9467aec002d0e62c682a" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY106<br>             <b>Area:</b> حي العزيزية<br>             <b>Orders:</b> 341<br>             <b>Distance:</b> 13.7 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_44b488cef017a24d489fae4c452593d9.setContent(html_0e2f730ad2ba9467aec002d0e62c682a);
            
        

        circle_marker_cfd7af3b490d1707840dc5c192b2d9e1.bindPopup(popup_44b488cef017a24d489fae4c452593d9)
        ;

        
    
    
            var circle_marker_518a73a38e7e508033b126a974441231 = L.circleMarker(
                [24.806236182336182, 46.7877831908832],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_49384fde1b065ed08879c84bd8e1d14b = L.popup({
  "maxWidth": 200,
});

        
            
                var html_722013c668b0006a57c7c75b5493a517 = $(`<div id="html_722013c668b0006a57c7c75b5493a517" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY17<br>             <b>Area:</b> حي اليرموك<br>             <b>Orders:</b> 351<br>             <b>Distance:</b> 13.2 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_49384fde1b065ed08879c84bd8e1d14b.setContent(html_722013c668b0006a57c7c75b5493a517);
            
        

        circle_marker_518a73a38e7e508033b126a974441231.bindPopup(popup_49384fde1b065ed08879c84bd8e1d14b)
        ;

        
    
    
            var circle_marker_a57494c8543f854a83c8f58dc8f9ff64 = L.circleMarker(
                [24.798971823204422, 46.74399392265193],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_6d598c5b7350f90eef56ccc44b1a3eac = L.popup({
  "maxWidth": 200,
});

        
            
                var html_760039a3234c746f105faec06cc841ba = $(`<div id="html_760039a3234c746f105faec06cc841ba" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY18<br>             <b>Area:</b> حي الشهداء<br>             <b>Orders:</b> 181<br>             <b>Distance:</b> 13.6 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_6d598c5b7350f90eef56ccc44b1a3eac.setContent(html_760039a3234c746f105faec06cc841ba);
            
        

        circle_marker_a57494c8543f854a83c8f58dc8f9ff64.bindPopup(popup_6d598c5b7350f90eef56ccc44b1a3eac)
        ;

        
    
    
            var circle_marker_33e2653940f9ab538ae605383445e841 = L.circleMarker(
                [24.77603373015873, 46.80958690476191],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_7e1179fa28b08e93e7b545c036741667 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_f5bcb05e1e51c2a088a69112c7335896 = $(`<div id="html_f5bcb05e1e51c2a088a69112c7335896" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY19<br>             <b>Area:</b> حي الخليج<br>             <b>Orders:</b> 252<br>             <b>Distance:</b> 9.9 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_7e1179fa28b08e93e7b545c036741667.setContent(html_f5bcb05e1e51c2a088a69112c7335896);
            
        

        circle_marker_33e2653940f9ab538ae605383445e841.bindPopup(popup_7e1179fa28b08e93e7b545c036741667)
        ;

        
    
    
            var circle_marker_56b7492b1290b6ff715d0557682de363 = L.circleMarker(
                [24.735963855421687, 46.766806626506025],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_47ad5020875bca9fd45e727beacf7610 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_4c584840e142c9013df2d7b9904b68b0 = $(`<div id="html_4c584840e142c9013df2d7b9904b68b0" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY20<br>             <b>Area:</b> حي الروضة<br>             <b>Orders:</b> 166<br>             <b>Distance:</b> 6.3 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_47ad5020875bca9fd45e727beacf7610.setContent(html_4c584840e142c9013df2d7b9904b68b0);
            
        

        circle_marker_56b7492b1290b6ff715d0557682de363.bindPopup(popup_47ad5020875bca9fd45e727beacf7610)
        ;

        
    
    
            var circle_marker_d9f4dd40603b42fcbe6e99664634c9b9 = L.circleMarker(
                [24.771155813953488, 46.700751162790695],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_f18f9b6f0f97e7ebea37976132825c51 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_542f61070573c4d690004dd18a6c49bf = $(`<div id="html_542f61070573c4d690004dd18a6c49bf" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY21<br>             <b>Area:</b> حي التعاون<br>             <b>Orders:</b> 86<br>             <b>Distance:</b> 13.7 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_f18f9b6f0f97e7ebea37976132825c51.setContent(html_542f61070573c4d690004dd18a6c49bf);
            
        

        circle_marker_d9f4dd40603b42fcbe6e99664634c9b9.bindPopup(popup_f18f9b6f0f97e7ebea37976132825c51)
        ;

        
    
    
            var circle_marker_b6daab352dda23339f7585c45aea7f0e = L.circleMarker(
                [24.76288510638298, 46.706653191489366],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_8ed0e5ec6a6e00dc1a3269fa5c073235 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_a68d85fec1cfb4cb529ab68967db9f5f = $(`<div id="html_a68d85fec1cfb4cb529ab68967db9f5f" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY22<br>             <b>Area:</b> An Nuzhah<br>             <b>Orders:</b> 47<br>             <b>Distance:</b> 12.6 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_8ed0e5ec6a6e00dc1a3269fa5c073235.setContent(html_a68d85fec1cfb4cb529ab68967db9f5f);
            
        

        circle_marker_b6daab352dda23339f7585c45aea7f0e.bindPopup(popup_8ed0e5ec6a6e00dc1a3269fa5c073235)
        ;

        
    
    
            var circle_marker_43648d3fe39d5a6c2f33be72506f1b67 = L.circleMarker(
                [24.73980888888889, 46.66540222222222],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_e9b0c95fd451ed6b8ab186d35fd39743 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_cbcf686fb9536e93fc3652c9be3a787e = $(`<div id="html_cbcf686fb9536e93fc3652c9be3a787e" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY25<br>             <b>Area:</b> حي الملك فهد<br>             <b>Orders:</b> 45<br>             <b>Distance:</b> 14.8 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_e9b0c95fd451ed6b8ab186d35fd39743.setContent(html_cbcf686fb9536e93fc3652c9be3a787e);
            
        

        circle_marker_43648d3fe39d5a6c2f33be72506f1b67.bindPopup(popup_e9b0c95fd451ed6b8ab186d35fd39743)
        ;

        
    
    
            var circle_marker_baa739b5cf503d5b103c2d72012c9b62 = L.circleMarker(
                [24.737047826086954, 46.83752666666667],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_67dc765f647e5875e48addcb3b2dd6f1 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_86a1cdeb5a5aa036c83bfc8081bd9e17 = $(`<div id="html_86a1cdeb5a5aa036c83bfc8081bd9e17" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY29<br>             <b>Area:</b> حي النسيم الشرقي<br>             <b>Orders:</b> 345<br>             <b>Distance:</b> 6.7 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_67dc765f647e5875e48addcb3b2dd6f1.setContent(html_86a1cdeb5a5aa036c83bfc8081bd9e17);
            
        

        circle_marker_baa739b5cf503d5b103c2d72012c9b62.bindPopup(popup_67dc765f647e5875e48addcb3b2dd6f1)
        ;

        
    
    
            var circle_marker_0b57bc05152ce5a974f9417c6442d771 = L.circleMarker(
                [24.679743076923074, 46.72444307692308],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_4c23500b002587f68db5b0c371957c40 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_c8f8eedccc34fa599119ae6552968fcf = $(`<div id="html_c8f8eedccc34fa599119ae6552968fcf" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY35<br>             <b>Area:</b> Ad Dhubbat<br>             <b>Orders:</b> 65<br>             <b>Distance:</b> 7.7 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_4c23500b002587f68db5b0c371957c40.setContent(html_c8f8eedccc34fa599119ae6552968fcf);
            
        

        circle_marker_0b57bc05152ce5a974f9417c6442d771.bindPopup(popup_4c23500b002587f68db5b0c371957c40)
        ;

        
    
    
            var circle_marker_35596ffd25ce84d8074a1a54919fbc6c = L.circleMarker(
                [24.660523076923077, 46.71022307692307],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_2db0000c46035f3e2c82f770dbf3fe50 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_ec5949cf266a1502eeeade3bfe7f07f8 = $(`<div id="html_ec5949cf266a1502eeeade3bfe7f07f8" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY36<br>             <b>Area:</b> حي المربع<br>             <b>Orders:</b> 39<br>             <b>Distance:</b> 9.6 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_2db0000c46035f3e2c82f770dbf3fe50.setContent(html_ec5949cf266a1502eeeade3bfe7f07f8);
            
        

        circle_marker_35596ffd25ce84d8074a1a54919fbc6c.bindPopup(popup_2db0000c46035f3e2c82f770dbf3fe50)
        ;

        
    
    
            var circle_marker_c43957e360ea1eee293705739ce2a63e = L.circleMarker(
                [24.61582, 46.67899517241379],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_7464589293d95e4d3a5b4e5b6c6808e1 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_abdc1c2fa83bfcbfdc2b6a119cb85f75 = $(`<div id="html_abdc1c2fa83bfcbfdc2b6a119cb85f75" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY37<br>             <b>Area:</b> حي البديعة<br>             <b>Orders:</b> 290<br>             <b>Distance:</b> 14.6 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_7464589293d95e4d3a5b4e5b6c6808e1.setContent(html_abdc1c2fa83bfcbfdc2b6a119cb85f75);
            
        

        circle_marker_c43957e360ea1eee293705739ce2a63e.bindPopup(popup_7464589293d95e4d3a5b4e5b6c6808e1)
        ;

        
    
    
            var circle_marker_20ccbc7ff37f3f6f2f08f51168c8c867 = L.circleMarker(
                [24.596309174311926, 46.7598876146789],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_24d7f65564646d5b8a0237519968d2d7 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_acfc26eec61aad72e3439d9ad958d4af = $(`<div id="html_acfc26eec61aad72e3439d9ad958d4af" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY41<br>             <b>Area:</b> حي العزيزية<br>             <b>Orders:</b> 218<br>             <b>Distance:</b> 11.0 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_24d7f65564646d5b8a0237519968d2d7.setContent(html_acfc26eec61aad72e3439d9ad958d4af);
            
        

        circle_marker_20ccbc7ff37f3f6f2f08f51168c8c867.bindPopup(popup_24d7f65564646d5b8a0237519968d2d7)
        ;

        
    
    
            var circle_marker_c8f45314d6d534fc9ac73cf3917cdbcf = L.circleMarker(
                [24.57182545126354, 46.779927256317684],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_9a3cd0bee7253dfd5059e9b27df5cd7c = L.popup({
  "maxWidth": 200,
});

        
            
                var html_0b17804f496c489552a5677c27ccb193 = $(`<div id="html_0b17804f496c489552a5677c27ccb193" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY43<br>             <b>Area:</b> حي العزيزية<br>             <b>Orders:</b> 554<br>             <b>Distance:</b> 13.0 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_9a3cd0bee7253dfd5059e9b27df5cd7c.setContent(html_0b17804f496c489552a5677c27ccb193);
            
        

        circle_marker_c8f45314d6d534fc9ac73cf3917cdbcf.bindPopup(popup_9a3cd0bee7253dfd5059e9b27df5cd7c)
        ;

        
    
    
            var circle_marker_f1015247bc1c7a5930f83285e12b645f = L.circleMarker(
                [24.555278140703518, 46.803108542713574],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_ea3ec755ad4c54ccb582b7958c317138 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_bfcdfe70d870db45e01a9cf33a2a943c = $(`<div id="html_bfcdfe70d870db45e01a9cf33a2a943c" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY44<br>             <b>Area:</b> حي الدار البيضاء<br>             <b>Orders:</b> 398<br>             <b>Distance:</b> 14.7 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_ea3ec755ad4c54ccb582b7958c317138.setContent(html_bfcdfe70d870db45e01a9cf33a2a943c);
            
        

        circle_marker_f1015247bc1c7a5930f83285e12b645f.bindPopup(popup_ea3ec755ad4c54ccb582b7958c317138)
        ;

        
    
    
            var circle_marker_cad86b0058dffa6cd03ad9294ab2c499 = L.circleMarker(
                [24.81432046783626, 46.822449122807015],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_c2d57c5d47c12794b9df06a270f2ec95 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_09240bf2730c64fffe0540f461a9b0d4 = $(`<div id="html_09240bf2730c64fffe0540f461a9b0d4" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY55<br>             <b>Area:</b> حي القادسية<br>             <b>Orders:</b> 171<br>             <b>Distance:</b> 14.3 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_c2d57c5d47c12794b9df06a270f2ec95.setContent(html_09240bf2730c64fffe0540f461a9b0d4);
            
        

        circle_marker_cad86b0058dffa6cd03ad9294ab2c499.bindPopup(popup_c2d57c5d47c12794b9df06a270f2ec95)
        ;

        
    
    
            var circle_marker_0819dad98514f9bbed1a1b539f1873e4 = L.circleMarker(
                [24.7773487394958, 46.763592857142854],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_701984753569e3224126d644da2366fc = L.popup({
  "maxWidth": 200,
});

        
            
                var html_1bdf641c3d56ccad0fb2ffc9ae04fc37 = $(`<div id="html_1bdf641c3d56ccad0fb2ffc9ae04fc37" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY56<br>             <b>Area:</b> حي الحمراء<br>             <b>Orders:</b> 238<br>             <b>Distance:</b> 10.6 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_701984753569e3224126d644da2366fc.setContent(html_1bdf641c3d56ccad0fb2ffc9ae04fc37);
            
        

        circle_marker_0819dad98514f9bbed1a1b539f1873e4.bindPopup(popup_701984753569e3224126d644da2366fc)
        ;

        
    
    
            var circle_marker_0983cc9476a9fd52b8a8e0379f23252f = L.circleMarker(
                [24.76172127071823, 46.774360220994474],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_2775671e2fdf15b3e2b4d6d9b0c73ba6 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_9b33f7b810fdc2dcdad983b171a6e967 = $(`<div id="html_9b33f7b810fdc2dcdad983b171a6e967" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY57<br>             <b>Area:</b> حي الملك فيصل<br>             <b>Orders:</b> 362<br>             <b>Distance:</b> 8.6 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_2775671e2fdf15b3e2b4d6d9b0c73ba6.setContent(html_9b33f7b810fdc2dcdad983b171a6e967);
            
        

        circle_marker_0983cc9476a9fd52b8a8e0379f23252f.bindPopup(popup_2775671e2fdf15b3e2b4d6d9b0c73ba6)
        ;

        
    
    
            var circle_marker_db35f4862de93d240887a67125f3719a = L.circleMarker(
                [24.77704, 46.835139285714284],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_a387ce7cb86a8df47983c8bac2415d0f = L.popup({
  "maxWidth": 200,
});

        
            
                var html_c9bd0fd4c67c84e5c02b4471534d0faa = $(`<div id="html_c9bd0fd4c67c84e5c02b4471534d0faa" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY58<br>             <b>Area:</b> حي النهضة<br>             <b>Orders:</b> 140<br>             <b>Distance:</b> 10.5 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_a387ce7cb86a8df47983c8bac2415d0f.setContent(html_c9bd0fd4c67c84e5c02b4471534d0faa);
            
        

        circle_marker_db35f4862de93d240887a67125f3719a.bindPopup(popup_a387ce7cb86a8df47983c8bac2415d0f)
        ;

        
    
    
            var circle_marker_a1d597e7492f63bdd3e67209ad4e88af = L.circleMarker(
                [24.75913183183183, 46.8172048048048],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_d9c951760d3d72fff466f8b814a099d4 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_b7636a960f917860fd7ce944f40785b0 = $(`<div id="html_b7636a960f917860fd7ce944f40785b0" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY59<br>             <b>Area:</b> حي النهضة<br>             <b>Orders:</b> 333<br>             <b>Distance:</b> 8.1 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_d9c951760d3d72fff466f8b814a099d4.setContent(html_b7636a960f917860fd7ce944f40785b0);
            
        

        circle_marker_a1d597e7492f63bdd3e67209ad4e88af.bindPopup(popup_d9c951760d3d72fff466f8b814a099d4)
        ;

        
    
    
            var circle_marker_7613a3df82ce2951d980a97aeacee201 = L.circleMarker(
                [24.78398950617284, 46.71446975308642],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_99f006e7c3f9b75f6e03ebb88e1506e8 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_217d949f3512442670dbe7bd277b46de = $(`<div id="html_217d949f3512442670dbe7bd277b46de" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY60<br>             <b>Area:</b> Al Izdihar<br>             <b>Orders:</b> 162<br>             <b>Distance:</b> 13.8 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_99f006e7c3f9b75f6e03ebb88e1506e8.setContent(html_217d949f3512442670dbe7bd277b46de);
            
        

        circle_marker_7613a3df82ce2951d980a97aeacee201.bindPopup(popup_99f006e7c3f9b75f6e03ebb88e1506e8)
        ;

        
    
    
            var circle_marker_da81acc5b0e68deb4fce2d565bd1ab37 = L.circleMarker(
                [24.764391262135923, 46.72599611650486],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_a3b70897ae2e3aaff29816497f94313c = L.popup({
  "maxWidth": 200,
});

        
            
                var html_4b751aae1ad4ef241cba5d72d7782f39 = $(`<div id="html_4b751aae1ad4ef241cba5d72d7782f39" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY61<br>             <b>Area:</b> حي المغرزات<br>             <b>Orders:</b> 103<br>             <b>Distance:</b> 11.4 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_a3b70897ae2e3aaff29816497f94313c.setContent(html_4b751aae1ad4ef241cba5d72d7782f39);
            
        

        circle_marker_da81acc5b0e68deb4fce2d565bd1ab37.bindPopup(popup_a3b70897ae2e3aaff29816497f94313c)
        ;

        
    
    
            var circle_marker_214844faea6edd2df04b5083d8a24495 = L.circleMarker(
                [24.742572916666663, 46.694475000000004],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_1d4b39fbe0cfd83102789c9e01d3c84d = L.popup({
  "maxWidth": 200,
});

        
            
                var html_fc2c0ae6142bd1514db5def0649f1ac4 = $(`<div id="html_fc2c0ae6142bd1514db5def0649f1ac4" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY62<br>             <b>Area:</b> حي الواحة<br>             <b>Orders:</b> 48<br>             <b>Distance:</b> 12.3 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_1d4b39fbe0cfd83102789c9e01d3c84d.setContent(html_fc2c0ae6142bd1514db5def0649f1ac4);
            
        

        circle_marker_214844faea6edd2df04b5083d8a24495.bindPopup(popup_1d4b39fbe0cfd83102789c9e01d3c84d)
        ;

        
    
    
            var circle_marker_50b5888a51b3bff6ed6afc553dac9769 = L.circleMarker(
                [24.75051320754717, 46.721241509433966],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_b0c6e3b4917acad863c383efaec17e1e = L.popup({
  "maxWidth": 200,
});

        
            
                var html_9d1050a970884c7eb8949d013f809f14 = $(`<div id="html_9d1050a970884c7eb8949d013f809f14" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY63<br>             <b>Area:</b> حي النزهة<br>             <b>Orders:</b> 53<br>             <b>Distance:</b> 10.6 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_b0c6e3b4917acad863c383efaec17e1e.setContent(html_9d1050a970884c7eb8949d013f809f14);
            
        

        circle_marker_50b5888a51b3bff6ed6afc553dac9769.bindPopup(popup_b0c6e3b4917acad863c383efaec17e1e)
        ;

        
    
    
            var circle_marker_a7fef62faa4d0ddf7e78a42e5c534060 = L.circleMarker(
                [24.728632941176468, 46.67606941176471],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_baa45b5f5c00ae9b94c849c7bd66c906 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_f377186e2d96e57e458af5ddfcf605ef = $(`<div id="html_f377186e2d96e57e458af5ddfcf605ef" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY64<br>             <b>Area:</b> حي الورود<br>             <b>Orders:</b> 85<br>             <b>Distance:</b> 13.4 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_baa45b5f5c00ae9b94c849c7bd66c906.setContent(html_f377186e2d96e57e458af5ddfcf605ef);
            
        

        circle_marker_a7fef62faa4d0ddf7e78a42e5c534060.bindPopup(popup_baa45b5f5c00ae9b94c849c7bd66c906)
        ;

        
    
    
            var circle_marker_e492ea33f8643f59594e4c70f542e98c = L.circleMarker(
                [24.71277234042553, 46.685778723404255],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_19111113458bc2d3d9dd7a22609b54f2 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_ea0fb43c9cc3644d2cf8e98c5f571138 = $(`<div id="html_ea0fb43c9cc3644d2cf8e98c5f571138" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY65<br>             <b>Area:</b> As Sulimaniyah<br>             <b>Orders:</b> 47<br>             <b>Distance:</b> 11.9 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_19111113458bc2d3d9dd7a22609b54f2.setContent(html_ea0fb43c9cc3644d2cf8e98c5f571138);
            
        

        circle_marker_e492ea33f8643f59594e4c70f542e98c.bindPopup(popup_19111113458bc2d3d9dd7a22609b54f2)
        ;

        
    
    
            var circle_marker_30eed11d0c3fbbac881ea1a01a8bccbf = L.circleMarker(
                [24.695515217391304, 46.70632173913044],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_8a4f01e4f54371184a2c9dd8613a9b2a = L.popup({
  "maxWidth": 200,
});

        
            
                var html_debd501e4befb23292c0c34bf7c00d11 = $(`<div id="html_debd501e4befb23292c0c34bf7c00d11" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY66<br>             <b>Area:</b> حي السليمانية<br>             <b>Orders:</b> 46<br>             <b>Distance:</b> 9.5 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_8a4f01e4f54371184a2c9dd8613a9b2a.setContent(html_debd501e4befb23292c0c34bf7c00d11);
            
        

        circle_marker_30eed11d0c3fbbac881ea1a01a8bccbf.bindPopup(popup_8a4f01e4f54371184a2c9dd8613a9b2a)
        ;

        
    
    
            var circle_marker_05bb673ef769a2dae8006bd86e035fcc = L.circleMarker(
                [24.68482340425532, 46.6999744680851],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_a238f24f9214a46ce47a693e8f09169b = L.popup({
  "maxWidth": 200,
});

        
            
                var html_39e0c0b4f3d0200e7cd68143173fe1db = $(`<div id="html_39e0c0b4f3d0200e7cd68143173fe1db" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY67<br>             <b>Area:</b> Al Olaya<br>             <b>Orders:</b> 94<br>             <b>Distance:</b> 10.1 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_a238f24f9214a46ce47a693e8f09169b.setContent(html_39e0c0b4f3d0200e7cd68143173fe1db);
            
        

        circle_marker_05bb673ef769a2dae8006bd86e035fcc.bindPopup(popup_a238f24f9214a46ce47a693e8f09169b)
        ;

        
    
    
            var circle_marker_441e760061c07e28e36a2deb24469426 = L.circleMarker(
                [24.71727125, 46.75521375],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_13dcde2d7043f1cc9319d9b56cc40045 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_946334fb7d5a7a2ffb1495940ab80266 = $(`<div id="html_946334fb7d5a7a2ffb1495940ab80266" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY68<br>             <b>Area:</b> حي الروضة<br>             <b>Orders:</b> 80<br>             <b>Distance:</b> 5.6 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_13dcde2d7043f1cc9319d9b56cc40045.setContent(html_946334fb7d5a7a2ffb1495940ab80266);
            
        

        circle_marker_441e760061c07e28e36a2deb24469426.bindPopup(popup_13dcde2d7043f1cc9319d9b56cc40045)
        ;

        
    
    
            var circle_marker_8dffcceac653d79ac4074485fd052cc1 = L.circleMarker(
                [24.721958, 46.83955733333334],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_3492c4d6235ace5872603f4565f61a34 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_2e9df3886aff8e2a4ece9bb81ca1a367 = $(`<div id="html_2e9df3886aff8e2a4ece9bb81ca1a367" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY7<br>             <b>Area:</b> حي النسيم الغربي<br>             <b>Orders:</b> 150<br>             <b>Distance:</b> 5.5 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_3492c4d6235ace5872603f4565f61a34.setContent(html_2e9df3886aff8e2a4ece9bb81ca1a367);
            
        

        circle_marker_8dffcceac653d79ac4074485fd052cc1.bindPopup(popup_3492c4d6235ace5872603f4565f61a34)
        ;

        
    
    
            var circle_marker_bc95dc6146be21f25f5e0a298dc34c97 = L.circleMarker(
                [24.715577848101265, 46.676805696202536],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_f2013e29d33539cede90fb30bad3e228 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_883bbf0d3556583df98c4da7dd924a45 = $(`<div id="html_883bbf0d3556583df98c4da7dd924a45" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY74<br>             <b>Area:</b> Al Wurud<br>             <b>Orders:</b> 158<br>             <b>Distance:</b> 12.9 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_f2013e29d33539cede90fb30bad3e228.setContent(html_883bbf0d3556583df98c4da7dd924a45);
            
        

        circle_marker_bc95dc6146be21f25f5e0a298dc34c97.bindPopup(popup_f2013e29d33539cede90fb30bad3e228)
        ;

        
    
    
            var circle_marker_110e206adc05f5d756fc3be33d5837ae = L.circleMarker(
                [24.707601149425287, 46.670322988505745],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_3d4d431680a8caac6fb917ddf5ad6f90 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_4ff8ac762dc3b14dd1395a38be1d78a3 = $(`<div id="html_4ff8ac762dc3b14dd1395a38be1d78a3" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY76<br>             <b>Area:</b> Al Olaya<br>             <b>Orders:</b> 87<br>             <b>Distance:</b> 13.3 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_3d4d431680a8caac6fb917ddf5ad6f90.setContent(html_4ff8ac762dc3b14dd1395a38be1d78a3);
            
        

        circle_marker_110e206adc05f5d756fc3be33d5837ae.bindPopup(popup_3d4d431680a8caac6fb917ddf5ad6f90)
        ;

        
    
    
            var circle_marker_f9ba6d87353cdd15bea3d04cd3eac712 = L.circleMarker(
                [24.69076206896552, 46.67078965517241],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_a1a9890c86825ff57048d9c80967cba8 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_d75fc674997b472cfd74166fc7a54511 = $(`<div id="html_d75fc674997b472cfd74166fc7a54511" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY77<br>             <b>Area:</b> حي العليا<br>             <b>Orders:</b> 58<br>             <b>Distance:</b> 13.1 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_a1a9890c86825ff57048d9c80967cba8.setContent(html_d75fc674997b472cfd74166fc7a54511);
            
        

        circle_marker_f9ba6d87353cdd15bea3d04cd3eac712.bindPopup(popup_a1a9890c86825ff57048d9c80967cba8)
        ;

        
    
    
            var circle_marker_22c21a048ea69240138567a5fd95331b = L.circleMarker(
                [24.686015714285713, 46.65803428571428],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_3150ac8d43014507d2b63ca65117309d = L.popup({
  "maxWidth": 200,
});

        
            
                var html_f015ec0f93787b08c7aca229e6814f26 = $(`<div id="html_f015ec0f93787b08c7aca229e6814f26" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY78<br>             <b>Area:</b> Umm Al Hamam Al Gharbi<br>             <b>Orders:</b> 70<br>             <b>Distance:</b> 14.4 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_3150ac8d43014507d2b63ca65117309d.setContent(html_f015ec0f93787b08c7aca229e6814f26);
            
        

        circle_marker_22c21a048ea69240138567a5fd95331b.bindPopup(popup_3150ac8d43014507d2b63ca65117309d)
        ;

        
    
    
            var circle_marker_832cf1c2e085adf52a619af168ebe109 = L.circleMarker(
                [24.757270588235293, 46.707911764705884],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_d97cb14925e9cd729f58f3f92a47b23e = L.popup({
  "maxWidth": 200,
});

        
            
                var html_246310feb43a7a44e62ac69802ff92e7 = $(`<div id="html_246310feb43a7a44e62ac69802ff92e7" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY82<br>             <b>Area:</b> حي النزهة<br>             <b>Orders:</b> 17<br>             <b>Distance:</b> 12.1 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_d97cb14925e9cd729f58f3f92a47b23e.setContent(html_246310feb43a7a44e62ac69802ff92e7);
            
        

        circle_marker_832cf1c2e085adf52a619af168ebe109.bindPopup(popup_d97cb14925e9cd729f58f3f92a47b23e)
        ;

        
    
    
            var circle_marker_f4923652a92629792499d511ff031d24 = L.circleMarker(
                [24.695557142857144, 46.85191428571428],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_a694fa4a16a18b88974872d19b0d4e6d = L.popup({
  "maxWidth": 200,
});

        
            
                var html_d48a89448f02d42af743e4723acea1c8 = $(`<div id="html_d48a89448f02d42af743e4723acea1c8" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY87<br>             <b>Area:</b> حي السعادة<br>             <b>Orders:</b> 7<br>             <b>Distance:</b> 5.3 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_a694fa4a16a18b88974872d19b0d4e6d.setContent(html_d48a89448f02d42af743e4723acea1c8);
            
        

        circle_marker_f4923652a92629792499d511ff031d24.bindPopup(popup_a694fa4a16a18b88974872d19b0d4e6d)
        ;

        
    
    
            var circle_marker_0a1ad89289421d6eb309161477674552 = L.circleMarker(
                [24.650708333333338, 46.88333333333333],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_4f57a23d64191ecc9b4cefbbedbb1231 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_260adcd13688668c7ce735232590e48f = $(`<div id="html_260adcd13688668c7ce735232590e48f" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY88<br>             <b>Area:</b> Khashm al 'An<br>             <b>Orders:</b> 12<br>             <b>Distance:</b> 9.3 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_4f57a23d64191ecc9b4cefbbedbb1231.setContent(html_260adcd13688668c7ce735232590e48f);
            
        

        circle_marker_0a1ad89289421d6eb309161477674552.bindPopup(popup_4f57a23d64191ecc9b4cefbbedbb1231)
        ;

        
    
    
            var circle_marker_0c2087942c34ca9ed06e86490cdb5cb0 = L.circleMarker(
                [24.658255263157894, 46.85028421052632],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_06c8b7137b0974d9240a064fe3d5e5f8 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_183e371dba20d72dc25985ab05da1555 = $(`<div id="html_183e371dba20d72dc25985ab05da1555" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY89<br>             <b>Area:</b> Khashm al 'An<br>             <b>Orders:</b> 38<br>             <b>Distance:</b> 6.0 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_06c8b7137b0974d9240a064fe3d5e5f8.setContent(html_183e371dba20d72dc25985ab05da1555);
            
        

        circle_marker_0c2087942c34ca9ed06e86490cdb5cb0.bindPopup(popup_06c8b7137b0974d9240a064fe3d5e5f8)
        ;

        
    
    
            var circle_marker_568bd6bb4fd8f45de6197f9f5122cd58 = L.circleMarker(
                [24.5748, 46.832750000000004],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_cdf35fc35d4737b9b06557c65c89209e = L.popup({
  "maxWidth": 200,
});

        
            
                var html_316ffa2573d4a55a8c8b3c9105786e39 = $(`<div id="html_316ffa2573d4a55a8c8b3c9105786e39" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY90<br>             <b>Area:</b> حي الاسكان<br>             <b>Orders:</b> 2<br>             <b>Distance:</b> 13.0 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_cdf35fc35d4737b9b06557c65c89209e.setContent(html_316ffa2573d4a55a8c8b3c9105786e39);
            
        

        circle_marker_568bd6bb4fd8f45de6197f9f5122cd58.bindPopup(popup_cdf35fc35d4737b9b06557c65c89209e)
        ;

        
    
    
            var circle_marker_108543dd00010ee7ffc2b435e7ebceef = L.circleMarker(
                [24.571403703703705, 46.84393703703704],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_d38b846c24612feb4bd7b54eb546951e = L.popup({
  "maxWidth": 200,
});

        
            
                var html_3398647bb47c772db02b07535cc85de9 = $(`<div id="html_3398647bb47c772db02b07535cc85de9" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY91<br>             <b>Area:</b> حي الاسكان<br>             <b>Orders:</b> 27<br>             <b>Distance:</b> 13.7 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_d38b846c24612feb4bd7b54eb546951e.setContent(html_3398647bb47c772db02b07535cc85de9);
            
        

        circle_marker_108543dd00010ee7ffc2b435e7ebceef.bindPopup(popup_d38b846c24612feb4bd7b54eb546951e)
        ;

        
    
    
            var circle_marker_42c5cadac8c8b2fc702b75c04869192c = L.circleMarker(
                [24.75115185185185, 46.711642222222224],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_612e4aea2a482e388760a2d7b99bbcda = L.popup({
  "maxWidth": 200,
});

        
            
                var html_9beca6c2867e831f8b401f200c19dbe0 = $(`<div id="html_9beca6c2867e831f8b401f200c19dbe0" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY92<br>             <b>Area:</b> An Nuzhah<br>             <b>Orders:</b> 135<br>             <b>Distance:</b> 11.4 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_612e4aea2a482e388760a2d7b99bbcda.setContent(html_9beca6c2867e831f8b401f200c19dbe0);
            
        

        circle_marker_42c5cadac8c8b2fc702b75c04869192c.bindPopup(popup_612e4aea2a482e388760a2d7b99bbcda)
        ;

        
    
    
            var circle_marker_c59ae7edea45134118199e0ad35ba880 = L.circleMarker(
                [24.675734, 46.749016000000005],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_c95dceb254220981b7bf6224fc3ffef8 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_632e70962485ac1da5378c286748c2b8 = $(`<div id="html_632e70962485ac1da5378c286748c2b8" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY93<br>             <b>Area:</b> Ar Rabwah<br>             <b>Orders:</b> 50<br>             <b>Distance:</b> 5.4 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_c95dceb254220981b7bf6224fc3ffef8.setContent(html_632e70962485ac1da5378c286748c2b8);
            
        

        circle_marker_c59ae7edea45134118199e0ad35ba880.bindPopup(popup_c95dceb254220981b7bf6224fc3ffef8)
        ;

        
    
    
            var circle_marker_1c6401995c9a36c69898cf6a7ecbf425 = L.circleMarker(
                [24.65589019607843, 46.739187254901964],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_945b11474d2c22f7aa4e78df3d3dbce4 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_f1fc8534fe7f17e62822b4c4ca85e7b1 = $(`<div id="html_f1fc8534fe7f17e62822b4c4ca85e7b1" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY94<br>             <b>Area:</b> حي الملز<br>             <b>Orders:</b> 102<br>             <b>Distance:</b> 7.1 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_945b11474d2c22f7aa4e78df3d3dbce4.setContent(html_f1fc8534fe7f17e62822b4c4ca85e7b1);
            
        

        circle_marker_1c6401995c9a36c69898cf6a7ecbf425.bindPopup(popup_945b11474d2c22f7aa4e78df3d3dbce4)
        ;

        
    
    
            var circle_marker_92cbe533e87b7836a641be46ae865801 = L.circleMarker(
                [24.630663636363636, 46.72485],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_6f9178906f6adb2d736e46376d8e6e3f = L.popup({
  "maxWidth": 200,
});

        
            
                var html_b7e1bbc7d39124c509f69b433993121f = $(`<div id="html_b7e1bbc7d39124c509f69b433993121f" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY96<br>             <b>Area:</b> Almarqab<br>             <b>Orders:</b> 110<br>             <b>Distance:</b> 9.9 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_6f9178906f6adb2d736e46376d8e6e3f.setContent(html_b7e1bbc7d39124c509f69b433993121f);
            
        

        circle_marker_92cbe533e87b7836a641be46ae865801.bindPopup(popup_6f9178906f6adb2d736e46376d8e6e3f)
        ;

        
    
    
            var circle_marker_4f12201c25b870de17569fa77a85b229 = L.circleMarker(
                [24.631566463414636, 46.7609737804878],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_ee49f4537a4dfa083ae4de3e5e7cac5e = L.popup({
  "maxWidth": 200,
});

        
            
                var html_9e3dffe3786e975a899941f0716288c2 = $(`<div id="html_9e3dffe3786e975a899941f0716288c2" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY97<br>             <b>Area:</b> Al Faisaliyyah<br>             <b>Orders:</b> 164<br>             <b>Distance:</b> 7.4 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_ee49f4537a4dfa083ae4de3e5e7cac5e.setContent(html_9e3dffe3786e975a899941f0716288c2);
            
        

        circle_marker_4f12201c25b870de17569fa77a85b229.bindPopup(popup_ee49f4537a4dfa083ae4de3e5e7cac5e)
        ;

        
    
    
            var circle_marker_48ef9d67cd3e3fe36fe6182408cd3f72 = L.circleMarker(
                [24.608267465753425, 46.727524657534246],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_6a1afbf3e6c3ac82be6122629b585ced = L.popup({
  "maxWidth": 200,
});

        
            
                var html_19f0551251dcc182ee46697a49311dfe = $(`<div id="html_19f0551251dcc182ee46697a49311dfe" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY98<br>             <b>Area:</b> حي المنصورة<br>             <b>Orders:</b> 292<br>             <b>Distance:</b> 11.5 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_6a1afbf3e6c3ac82be6122629b585ced.setContent(html_19f0551251dcc182ee46697a49311dfe);
            
        

        circle_marker_48ef9d67cd3e3fe36fe6182408cd3f72.bindPopup(popup_6a1afbf3e6c3ac82be6122629b585ced)
        ;

        
    
    
            var circle_marker_ccae3c55852b5d7028fa49fd5b8e302b = L.circleMarker(
                [24.656305681818182, 46.68721818181818],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_1e0b3e902fb1c3c0fe9e02abefe19459);
        
    
        var popup_a20035ff127433b056b5c68f95c8a977 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_4c4c4a5e591b981c44b0957b693ea08d = $(`<div id="html_4c4c4a5e591b981c44b0957b693ea08d" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY99<br>             <b>Area:</b> Al Murabba<br>             <b>Orders:</b> 88<br>             <b>Distance:</b> 11.9 km<br>             <b>Tier:</b> Tier 2             </div>`)[0];
                popup_a20035ff127433b056b5c68f95c8a977.setContent(html_4c4c4a5e591b981c44b0957b693ea08d);
            
        

        circle_marker_ccae3c55852b5d7028fa49fd5b8e302b.bindPopup(popup_a20035ff127433b056b5c68f95c8a977)
        ;

        
    
    
            feature_group_1e0b3e902fb1c3c0fe9e02abefe19459.addTo(map_c9740a3e5c262bf5eaf15ae69f13c876);
        
    
            var feature_group_70777dd09e6799d56805a52b0862801d = L.featureGroup(
                {
}
            );
        
    
            var circle_marker_8269c0c37e59b866dcd194e656e3c40d = L.circleMarker(
                [25.020205, 46.43456],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_93f9845b299075a9b441d0e2fc7fc8af = L.popup({
  "maxWidth": 200,
});

        
            
                var html_9796e27992c454d26c93ea94a6515bf2 = $(`<div id="html_9796e27992c454d26c93ea94a6515bf2" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> EPHP-025<br>             <b>Area:</b> حي النظيم<br>             <b>Orders:</b> 20<br>             <b>Distance:</b> 52.2 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_93f9845b299075a9b441d0e2fc7fc8af.setContent(html_9796e27992c454d26c93ea94a6515bf2);
            
        

        circle_marker_8269c0c37e59b866dcd194e656e3c40d.bindPopup(popup_93f9845b299075a9b441d0e2fc7fc8af)
        ;

        
    
    
            var circle_marker_064d1edda4d4dc1109307051077f7b7d = L.circleMarker(
                [25.48382, 49.15418],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_a73a78f3514f766d3e5d7a510cb7562b = L.popup({
  "maxWidth": 200,
});

        
            
                var html_683c1343c6f1239bfbba30fbcb6cefbc = $(`<div id="html_683c1343c6f1239bfbba30fbcb6cefbc" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> EPHP-026<br>             <b>Area:</b> Al Badiah<br>             <b>Orders:</b> 5<br>             <b>Distance:</b> 253.0 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_a73a78f3514f766d3e5d7a510cb7562b.setContent(html_683c1343c6f1239bfbba30fbcb6cefbc);
            
        

        circle_marker_064d1edda4d4dc1109307051077f7b7d.bindPopup(popup_a73a78f3514f766d3e5d7a510cb7562b)
        ;

        
    
    
            var circle_marker_f5cbc0cbbf0ad93f0a0fe0631cbb4595 = L.circleMarker(
                [24.826500000000003, 47.38605],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_44d6f0e720212f0bd9e97bdf08a59aae = L.popup({
  "maxWidth": 200,
});

        
            
                var html_e284cb2cb94ea0c834e33171babf88fd = $(`<div id="html_e284cb2cb94ea0c834e33171babf88fd" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> EPHP-030<br>             <b>Area:</b> حي الروابي<br>             <b>Orders:</b> 4<br>             <b>Distance:</b> 61.1 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_44d6f0e720212f0bd9e97bdf08a59aae.setContent(html_e284cb2cb94ea0c834e33171babf88fd);
            
        

        circle_marker_f5cbc0cbbf0ad93f0a0fe0631cbb4595.bindPopup(popup_44d6f0e720212f0bd9e97bdf08a59aae)
        ;

        
    
    
            var circle_marker_88a62e1b0e2ac74a6ba54a73231f85d4 = L.circleMarker(
                [26.28845, 50.11835],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_4ba146c3389a793a9d8220cecc53f69d = L.popup({
  "maxWidth": 200,
});

        
            
                var html_860feee3ba20b4f007a2be8fa47efb5b = $(`<div id="html_860feee3ba20b4f007a2be8fa47efb5b" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> EPHP-031<br>             <b>Area:</b> حي مدينة العمال<br>             <b>Orders:</b> 2<br>             <b>Distance:</b> 377.6 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_4ba146c3389a793a9d8220cecc53f69d.setContent(html_860feee3ba20b4f007a2be8fa47efb5b);
            
        

        circle_marker_88a62e1b0e2ac74a6ba54a73231f85d4.bindPopup(popup_4ba146c3389a793a9d8220cecc53f69d)
        ;

        
    
    
            var circle_marker_8688e4245b25fd1ffb355ddf94701c17 = L.circleMarker(
                [24.86465483870968, 46.82447903225807],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_dc9c6c978df4c9fa5f6ac26f1167f489 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_db380cdaa25c8c87c2a3e6cd1ff46c47 = $(`<div id="html_db380cdaa25c8c87c2a3e6cd1ff46c47" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY1<br>             <b>Area:</b> حي الرمال<br>             <b>Orders:</b> 62<br>             <b>Distance:</b> 19.8 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_dc9c6c978df4c9fa5f6ac26f1167f489.setContent(html_db380cdaa25c8c87c2a3e6cd1ff46c47);
            
        

        circle_marker_8688e4245b25fd1ffb355ddf94701c17.bindPopup(popup_dc9c6c978df4c9fa5f6ac26f1167f489)
        ;

        
    
    
            var circle_marker_74a8892879b13ffcc6562c1887c3a881 = L.circleMarker(
                [24.575035230352302, 46.555144850948516],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_a081c5c38b113623e69a26e60f33064b = L.popup({
  "maxWidth": 200,
});

        
            
                var html_1bf82f941974e16e49c43ecb564d7d12 = $(`<div id="html_1bf82f941974e16e49c43ecb564d7d12" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY10<br>             <b>Area:</b> حي طويق<br>             <b>Orders:</b> 738<br>             <b>Distance:</b> 27.8 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_a081c5c38b113623e69a26e60f33064b.setContent(html_1bf82f941974e16e49c43ecb564d7d12);
            
        

        circle_marker_74a8892879b13ffcc6562c1887c3a881.bindPopup(popup_a081c5c38b113623e69a26e60f33064b)
        ;

        
    
    
            var circle_marker_cde79c42dfb34981b3ff8e3fd47c5dfd = L.circleMarker(
                [24.62328231707317, 46.612473780487804],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_fda7d71cbdb1af3e1d55e53c68562685 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_e311b954aa1ef264ec8365416d751664 = $(`<div id="html_e311b954aa1ef264ec8365416d751664" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY101<br>             <b>Area:</b> حي العريجاء الوسطى<br>             <b>Orders:</b> 164<br>             <b>Distance:</b> 20.3 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_fda7d71cbdb1af3e1d55e53c68562685.setContent(html_e311b954aa1ef264ec8365416d751664);
            
        

        circle_marker_cde79c42dfb34981b3ff8e3fd47c5dfd.bindPopup(popup_fda7d71cbdb1af3e1d55e53c68562685)
        ;

        
    
    
            var circle_marker_8b96a2efb1ed35e59d6cb628c9be2310 = L.circleMarker(
                [24.558427272727272, 46.595229437229435],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_02b606abbfff2f91aa1bea89932f1b0d = L.popup({
  "maxWidth": 200,
});

        
            
                var html_005bc2d9cb10f30a2706e3a504c0027e = $(`<div id="html_005bc2d9cb10f30a2706e3a504c0027e" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY103<br>             <b>Area:</b> حي العوالي<br>             <b>Orders:</b> 231<br>             <b>Distance:</b> 25.2 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_02b606abbfff2f91aa1bea89932f1b0d.setContent(html_005bc2d9cb10f30a2706e3a504c0027e);
            
        

        circle_marker_8b96a2efb1ed35e59d6cb628c9be2310.bindPopup(popup_02b606abbfff2f91aa1bea89932f1b0d)
        ;

        
    
    
            var circle_marker_ab8322b0fb538b51064496bcd73c28c8 = L.circleMarker(
                [24.557719871794873, 46.86001089743589],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_104ae7e95f0bcaa846c230dbedf6c6af = L.popup({
  "maxWidth": 200,
});

        
            
                var html_aee9fe59a47b318200a3898bce8362b3 = $(`<div id="html_aee9fe59a47b318200a3898bce8362b3" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY105<br>             <b>Area:</b> Al Iskan<br>             <b>Orders:</b> 156<br>             <b>Distance:</b> 15.7 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_104ae7e95f0bcaa846c230dbedf6c6af.setContent(html_aee9fe59a47b318200a3898bce8362b3);
            
        

        circle_marker_ab8322b0fb538b51064496bcd73c28c8.bindPopup(popup_104ae7e95f0bcaa846c230dbedf6c6af)
        ;

        
    
    
            var circle_marker_f883354d72d901b60df7684be3e479d7 = L.circleMarker(
                [24.54070909090909, 46.68694589800444],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_a4bbef6bcb71a89f2f1210e6bb65f6a6 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_f163698b602ec918da834a44eb4bde81 = $(`<div id="html_f163698b602ec918da834a44eb4bde81" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY107<br>             <b>Area:</b> حي بدر<br>             <b>Orders:</b> 451<br>             <b>Distance:</b> 20.0 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_a4bbef6bcb71a89f2f1210e6bb65f6a6.setContent(html_f163698b602ec918da834a44eb4bde81);
            
        

        circle_marker_f883354d72d901b60df7684be3e479d7.bindPopup(popup_a4bbef6bcb71a89f2f1210e6bb65f6a6)
        ;

        
    
    
            var circle_marker_64f6d34afc8f893827827113a79c226e = L.circleMarker(
                [24.517212637362636, 46.666911538461534],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_6b59e29ea5f95923fd9be37bdd387b4a = L.popup({
  "maxWidth": 200,
});

        
            
                var html_f2ae5ef4eade02f5efc4ab169b7ac2ce = $(`<div id="html_f2ae5ef4eade02f5efc4ab169b7ac2ce" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY108<br>             <b>Area:</b> حي عكاظ<br>             <b>Orders:</b> 182<br>             <b>Distance:</b> 23.3 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_6b59e29ea5f95923fd9be37bdd387b4a.setContent(html_f2ae5ef4eade02f5efc4ab169b7ac2ce);
            
        

        circle_marker_64f6d34afc8f893827827113a79c226e.bindPopup(popup_6b59e29ea5f95923fd9be37bdd387b4a)
        ;

        
    
    
            var circle_marker_148e1ed0e8b02436e5fee01a5a808698 = L.circleMarker(
                [24.523469574944073, 46.65891476510067],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_c0bc1bb51a25e7f0b024dd8850dbf5ef = L.popup({
  "maxWidth": 200,
});

        
            
                var html_a4fb3398742df0e2ce26282f6ed969d5 = $(`<div id="html_a4fb3398742df0e2ce26282f6ed969d5" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY109<br>             <b>Area:</b> حي الحزم<br>             <b>Orders:</b> 447<br>             <b>Distance:</b> 23.2 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_c0bc1bb51a25e7f0b024dd8850dbf5ef.setContent(html_a4fb3398742df0e2ce26282f6ed969d5);
            
        

        circle_marker_148e1ed0e8b02436e5fee01a5a808698.bindPopup(popup_c0bc1bb51a25e7f0b024dd8850dbf5ef)
        ;

        
    
    
            var circle_marker_6909f691e78177b230a1aa707262ba91 = L.circleMarker(
                [24.794756989247315, 46.69450537634408],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_3ed39b9534466193e55243eb8b19803f = L.popup({
  "maxWidth": 200,
});

        
            
                var html_593ccc141ef69ae82c40d91162a87038 = $(`<div id="html_593ccc141ef69ae82c40d91162a87038" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY11<br>             <b>Area:</b> حي الوادي<br>             <b>Orders:</b> 93<br>             <b>Distance:</b> 16.0 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_3ed39b9534466193e55243eb8b19803f.setContent(html_593ccc141ef69ae82c40d91162a87038);
            
        

        circle_marker_6909f691e78177b230a1aa707262ba91.bindPopup(popup_3ed39b9534466193e55243eb8b19803f)
        ;

        
    
    
            var circle_marker_082fe9771300600dd5551ea5d790b4ab = L.circleMarker(
                [24.49541188118812, 46.622197029702974],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_419e560f869e7c2902a1fa0a56f64eab = L.popup({
  "maxWidth": 200,
});

        
            
                var html_da2fb3bd95372e55103a85760bb2de66 = $(`<div id="html_da2fb3bd95372e55103a85760bb2de66" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY110<br>             <b>Area:</b> حي ديراب<br>             <b>Orders:</b> 101<br>             <b>Distance:</b> 28.0 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_419e560f869e7c2902a1fa0a56f64eab.setContent(html_da2fb3bd95372e55103a85760bb2de66);
            
        

        circle_marker_082fe9771300600dd5551ea5d790b4ab.bindPopup(popup_419e560f869e7c2902a1fa0a56f64eab)
        ;

        
    
    
            var circle_marker_965b2e149eba6753d3218c3ba89ff590 = L.circleMarker(
                [24.87709375, 46.63676467391304],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_62dc061c839b6264e6adc3df4e9c2517 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_670402dd811566e017630278252fa887 = $(`<div id="html_670402dd811566e017630278252fa887" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY12<br>             <b>Area:</b> حي النرجس<br>             <b>Orders:</b> 368<br>             <b>Distance:</b> 26.8 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_62dc061c839b6264e6adc3df4e9c2517.setContent(html_670402dd811566e017630278252fa887);
            
        

        circle_marker_965b2e149eba6753d3218c3ba89ff590.bindPopup(popup_62dc061c839b6264e6adc3df4e9c2517)
        ;

        
    
    
            var circle_marker_e5955179e71776e5b7961658644795bd = L.circleMarker(
                [24.821174025974024, 46.60598311688312],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_a2c71628abe4c796aa1ff36ced254517 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_d35d4b9428736fdf17d197433468fcac = $(`<div id="html_d35d4b9428736fdf17d197433468fcac" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY13<br>             <b>Area:</b> Al Qirawan<br>             <b>Orders:</b> 77<br>             <b>Distance:</b> 24.6 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_a2c71628abe4c796aa1ff36ced254517.setContent(html_d35d4b9428736fdf17d197433468fcac);
            
        

        circle_marker_e5955179e71776e5b7961658644795bd.bindPopup(popup_a2c71628abe4c796aa1ff36ced254517)
        ;

        
    
    
            var circle_marker_d5c29f71021026535e150ecc1b62528a = L.circleMarker(
                [24.807566917293233, 46.60804736842105],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_f448a94507751c843e70b0267f7c5eea = L.popup({
  "maxWidth": 200,
});

        
            
                var html_5c05086e0de2fb448cff058e61e30a7c = $(`<div id="html_5c05086e0de2fb448cff058e61e30a7c" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY14<br>             <b>Area:</b> حي الملقا<br>             <b>Orders:</b> 133<br>             <b>Distance:</b> 23.6 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_f448a94507751c843e70b0267f7c5eea.setContent(html_5c05086e0de2fb448cff058e61e30a7c);
            
        

        circle_marker_d5c29f71021026535e150ecc1b62528a.bindPopup(popup_f448a94507751c843e70b0267f7c5eea)
        ;

        
    
    
            var circle_marker_f578e95f8f9f19b0ad855215626735e4 = L.circleMarker(
                [24.780732338308457, 46.62777860696517],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_9d9de448ba163667a77e0251a52c8645 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_138cddee38399900dc8d7608af3ce510 = $(`<div id="html_138cddee38399900dc8d7608af3ce510" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY15<br>             <b>Area:</b> حي العقيق<br>             <b>Orders:</b> 201<br>             <b>Distance:</b> 20.3 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_9d9de448ba163667a77e0251a52c8645.setContent(html_138cddee38399900dc8d7608af3ce510);
            
        

        circle_marker_f578e95f8f9f19b0ad855215626735e4.bindPopup(popup_9d9de448ba163667a77e0251a52c8645)
        ;

        
    
    
            var circle_marker_9fdba5d64bc874fa10ebdcd58693641e = L.circleMarker(
                [24.79004625, 46.577566250000004],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_f66999b48e6c94e5e4c8f461c6c2201c = L.popup({
  "maxWidth": 200,
});

        
            
                var html_057492893841d316a2f8da381b30b488 = $(`<div id="html_057492893841d316a2f8da381b30b488" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY16<br>             <b>Area:</b> Al Qirawan<br>             <b>Orders:</b> 80<br>             <b>Distance:</b> 25.2 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_f66999b48e6c94e5e4c8f461c6c2201c.setContent(html_057492893841d316a2f8da381b30b488);
            
        

        circle_marker_9fdba5d64bc874fa10ebdcd58693641e.bindPopup(popup_f66999b48e6c94e5e4c8f461c6c2201c)
        ;

        
    
    
            var circle_marker_ec3c3c9f86dab6fb6f5bf45d2daa031e = L.circleMarker(
                [24.802274675324675, 46.64256363636363],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_9e8924649422abcdb4ffa7e4fe0f7f87 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_b223c21194e333b692b112c936ee4cdd = $(`<div id="html_b223c21194e333b692b112c936ee4cdd" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY2<br>             <b>Area:</b> حي الصحافة<br>             <b>Orders:</b> 154<br>             <b>Distance:</b> 20.4 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_9e8924649422abcdb4ffa7e4fe0f7f87.setContent(html_b223c21194e333b692b112c936ee4cdd);
            
        

        circle_marker_ec3c3c9f86dab6fb6f5bf45d2daa031e.bindPopup(popup_9e8924649422abcdb4ffa7e4fe0f7f87)
        ;

        
    
    
            var circle_marker_2f99053233f1efdb33ac4d46625caae4 = L.circleMarker(
                [26.562385714285714, 50.00625714285714],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_42be6fdf1aac5e7fc43a6c0aba901e57 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_66538d99015f664b002eb63615081dee = $(`<div id="html_66538d99015f664b002eb63615081dee" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY205<br>             <b>Area:</b> حي الدبابية<br>             <b>Orders:</b> 7<br>             <b>Distance:</b> 383.1 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_42be6fdf1aac5e7fc43a6c0aba901e57.setContent(html_66538d99015f664b002eb63615081dee);
            
        

        circle_marker_2f99053233f1efdb33ac4d46625caae4.bindPopup(popup_42be6fdf1aac5e7fc43a6c0aba901e57)
        ;

        
    
    
            var circle_marker_749390066f052227a2154c51971451a7 = L.circleMarker(
                [26.546633333333332, 50.002399999999994],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_f60a4df7a0dff6771c6c9ca311fc38a7 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_bb96430af15fee6d167c8dab3f99c954 = $(`<div id="html_bb96430af15fee6d167c8dab3f99c954" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY206<br>             <b>Area:</b> حي الشويكة<br>             <b>Orders:</b> 9<br>             <b>Distance:</b> 381.8 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_f60a4df7a0dff6771c6c9ca311fc38a7.setContent(html_bb96430af15fee6d167c8dab3f99c954);
            
        

        circle_marker_749390066f052227a2154c51971451a7.bindPopup(popup_f60a4df7a0dff6771c6c9ca311fc38a7)
        ;

        
    
    
            var circle_marker_835389b5a69b1e5b018d6cf6fab4d728 = L.circleMarker(
                [26.551284042553196, 50.0002170212766],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_6794a078d3b22707476dd4bb61be7daa = L.popup({
  "maxWidth": 200,
});

        
            
                var html_608b620f41cb9a9a0c9102e5e11f595a = $(`<div id="html_608b620f41cb9a9a0c9102e5e11f595a" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY207<br>             <b>Area:</b> حي الكويكب<br>             <b>Orders:</b> 94<br>             <b>Distance:</b> 381.9 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_6794a078d3b22707476dd4bb61be7daa.setContent(html_608b620f41cb9a9a0c9102e5e11f595a);
            
        

        circle_marker_835389b5a69b1e5b018d6cf6fab4d728.bindPopup(popup_6794a078d3b22707476dd4bb61be7daa)
        ;

        
    
    
            var circle_marker_3b950fcd0b3f0e46a4a8bbd4543a64bd = L.circleMarker(
                [26.560680555555553, 49.99068333333334],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_ced31de87cd1350a0b2a799f28296b6a = L.popup({
  "maxWidth": 200,
});

        
            
                var html_9517e3ec76d92130f89a56af4694743c = $(`<div id="html_9517e3ec76d92130f89a56af4694743c" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY208<br>             <b>Area:</b> حي الخويلدية<br>             <b>Orders:</b> 36<br>             <b>Distance:</b> 381.7 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_ced31de87cd1350a0b2a799f28296b6a.setContent(html_9517e3ec76d92130f89a56af4694743c);
            
        

        circle_marker_3b950fcd0b3f0e46a4a8bbd4543a64bd.bindPopup(popup_ced31de87cd1350a0b2a799f28296b6a)
        ;

        
    
    
            var circle_marker_92655bad2479621b11cf86ee8f58666e = L.circleMarker(
                [26.568308333333334, 50.0094375],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_403bab70d1c047abc0b81a5a619219d1 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_2c60cda8986ed57cb4ffb9d746744ce6 = $(`<div id="html_2c60cda8986ed57cb4ffb9d746744ce6" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY209<br>             <b>Area:</b> حي الناصرة<br>             <b>Orders:</b> 48<br>             <b>Distance:</b> 383.7 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_403bab70d1c047abc0b81a5a619219d1.setContent(html_2c60cda8986ed57cb4ffb9d746744ce6);
            
        

        circle_marker_92655bad2479621b11cf86ee8f58666e.bindPopup(popup_403bab70d1c047abc0b81a5a619219d1)
        ;

        
    
    
            var circle_marker_cc2c1f295a36e24919e1d5e490ea6312 = L.circleMarker(
                [26.5580275, 50.046653750000004],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_90657abccf5ccf8814e3d9a5862b4f93 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_363a0cb47b508ba64cdc25b735c16c23 = $(`<div id="html_363a0cb47b508ba64cdc25b735c16c23" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY210<br>             <b>Area:</b> حي الصدفة<br>             <b>Orders:</b> 80<br>             <b>Distance:</b> 386.2 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_90657abccf5ccf8814e3d9a5862b4f93.setContent(html_363a0cb47b508ba64cdc25b735c16c23);
            
        

        circle_marker_cc2c1f295a36e24919e1d5e490ea6312.bindPopup(popup_90657abccf5ccf8814e3d9a5862b4f93)
        ;

        
    
    
            var circle_marker_1a81b2caebe2956a5be4736524c057cd = L.circleMarker(
                [26.569090909090907, 50.039092045454545],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_142c8d8eca9cbc398023eda69b1c9403 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_0348bf8a2ec38705eff4a703efbe826f = $(`<div id="html_0348bf8a2ec38705eff4a703efbe826f" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY211<br>             <b>Area:</b> حي الرضا<br>             <b>Orders:</b> 88<br>             <b>Distance:</b> 386.2 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_142c8d8eca9cbc398023eda69b1c9403.setContent(html_0348bf8a2ec38705eff4a703efbe826f);
            
        

        circle_marker_1a81b2caebe2956a5be4736524c057cd.bindPopup(popup_142c8d8eca9cbc398023eda69b1c9403)
        ;

        
    
    
            var circle_marker_24065e469d60ad9ecda6f806f5a0e956 = L.circleMarker(
                [26.57530843373494, 50.055707228915665],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_d8a566f6b7c0583e54c750d83e4b57a3 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_1b9c56d8e6f7e21fdc3f817945a470c4 = $(`<div id="html_1b9c56d8e6f7e21fdc3f817945a470c4" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY212<br>             <b>Area:</b> حي الروضة<br>             <b>Orders:</b> 166<br>             <b>Distance:</b> 388.0 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_d8a566f6b7c0583e54c750d83e4b57a3.setContent(html_1b9c56d8e6f7e21fdc3f817945a470c4);
            
        

        circle_marker_24065e469d60ad9ecda6f806f5a0e956.bindPopup(popup_d8a566f6b7c0583e54c750d83e4b57a3)
        ;

        
    
    
            var circle_marker_ef4ceb75e86ffc860b454140733915a4 = L.circleMarker(
                [26.590674324324326, 50.03576689189189],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_baf179c8dbfdafa88d26b3c3ec3faad3 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_f456f6b77b8f09eea17b52be138c0dbf = $(`<div id="html_f456f6b77b8f09eea17b52be138c0dbf" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY213<br>             <b>Area:</b> حي الروضة<br>             <b>Orders:</b> 148<br>             <b>Distance:</b> 387.2 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_baf179c8dbfdafa88d26b3c3ec3faad3.setContent(html_f456f6b77b8f09eea17b52be138c0dbf);
            
        

        circle_marker_ef4ceb75e86ffc860b454140733915a4.bindPopup(popup_baf179c8dbfdafa88d26b3c3ec3faad3)
        ;

        
    
    
            var circle_marker_92dafcaf8ef60ad23ab2ac243c73ac47 = L.circleMarker(
                [26.59821090909091, 49.98319818181818],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_94d51ba5da24a013e1787e36bc8446c4 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_436bf5d15abff3dbb44368cc9d9339b5 = $(`<div id="html_436bf5d15abff3dbb44368cc9d9339b5" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY214<br>             <b>Area:</b> حي النرجس<br>             <b>Orders:</b> 55<br>             <b>Distance:</b> 383.3 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_94d51ba5da24a013e1787e36bc8446c4.setContent(html_436bf5d15abff3dbb44368cc9d9339b5);
            
        

        circle_marker_92dafcaf8ef60ad23ab2ac243c73ac47.bindPopup(popup_94d51ba5da24a013e1787e36bc8446c4)
        ;

        
    
    
            var circle_marker_3dfa03d0537f7dfa0d94c98c0d1569b4 = L.circleMarker(
                [26.59615792682927, 49.97636646341464],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_b900967a0f4b693ab5afd84bc4f2563c = L.popup({
  "maxWidth": 200,
});

        
            
                var html_569be6638c650a19d25ec1db4f61915a = $(`<div id="html_569be6638c650a19d25ec1db4f61915a" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY215<br>             <b>Area:</b> حي القديح<br>             <b>Orders:</b> 164<br>             <b>Distance:</b> 382.6 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_b900967a0f4b693ab5afd84bc4f2563c.setContent(html_569be6638c650a19d25ec1db4f61915a);
            
        

        circle_marker_3dfa03d0537f7dfa0d94c98c0d1569b4.bindPopup(popup_b900967a0f4b693ab5afd84bc4f2563c)
        ;

        
    
    
            var circle_marker_3304fcdf5cdd9351ce7401ecd5f43f10 = L.circleMarker(
                [26.57703968253968, 49.98034126984127],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_6cd7b46f1d1667c1963f243a58bd97d0 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_7ecd77271e805d5f0eef3fbb634b1bfa = $(`<div id="html_7ecd77271e805d5f0eef3fbb634b1bfa" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY216<br>             <b>Area:</b> Al Qudaih<br>             <b>Orders:</b> 63<br>             <b>Distance:</b> 381.8 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_6cd7b46f1d1667c1963f243a58bd97d0.setContent(html_7ecd77271e805d5f0eef3fbb634b1bfa);
            
        

        circle_marker_3304fcdf5cdd9351ce7401ecd5f43f10.bindPopup(popup_6cd7b46f1d1667c1963f243a58bd97d0)
        ;

        
    
    
            var circle_marker_63fa1aae697d0c0544ad0175dad6ffa6 = L.circleMarker(
                [26.6373125, 49.96704375],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_59744fe5f2e81629ab5950a8f32ff5c9 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_41cc355f9e4485ad1eeae8fe3766e2b8 = $(`<div id="html_41cc355f9e4485ad1eeae8fe3766e2b8" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY217<br>             <b>Area:</b> Alyamamah<br>             <b>Orders:</b> 16<br>             <b>Distance:</b> 384.3 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_59744fe5f2e81629ab5950a8f32ff5c9.setContent(html_41cc355f9e4485ad1eeae8fe3766e2b8);
            
        

        circle_marker_63fa1aae697d0c0544ad0175dad6ffa6.bindPopup(popup_59744fe5f2e81629ab5950a8f32ff5c9)
        ;

        
    
    
            var circle_marker_5901e227d00d4e5d95dc21e6a8b72d28 = L.circleMarker(
                [26.571824242424242, 49.95244949494949],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_b480c1bb716bd78a046dae35ad388d96 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_75e333d70cc7aba2935a2b2a751dbab9 = $(`<div id="html_75e333d70cc7aba2935a2b2a751dbab9" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY218<br>             <b>Area:</b> حي الحزم<br>             <b>Orders:</b> 99<br>             <b>Distance:</b> 379.1 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_b480c1bb716bd78a046dae35ad388d96.setContent(html_75e333d70cc7aba2935a2b2a751dbab9);
            
        

        circle_marker_5901e227d00d4e5d95dc21e6a8b72d28.bindPopup(popup_b480c1bb716bd78a046dae35ad388d96)
        ;

        
    
    
            var circle_marker_25e6e7fe2594d186e7ad3e356a2efdef = L.circleMarker(
                [26.62935154185022, 49.96679911894273],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_09f2c18f875144ab80ebe149fb05808c = L.popup({
  "maxWidth": 200,
});

        
            
                var html_4fbe1ff90233151f8809476164ce94ef = $(`<div id="html_4fbe1ff90233151f8809476164ce94ef" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY219<br>             <b>Area:</b> حي الجميمة<br>             <b>Orders:</b> 227<br>             <b>Distance:</b> 383.8 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_09f2c18f875144ab80ebe149fb05808c.setContent(html_4fbe1ff90233151f8809476164ce94ef);
            
        

        circle_marker_25e6e7fe2594d186e7ad3e356a2efdef.bindPopup(popup_09f2c18f875144ab80ebe149fb05808c)
        ;

        
    
    
            var circle_marker_3793854e378044d2e130223b793fe1f1 = L.circleMarker(
                [26.132266666666666, 50.1433],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_d87740dccadb4440c1edbfcb8c028a67 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_fdf6801a366bce7b41b7c2689cef48cb = $(`<div id="html_fdf6801a366bce7b41b7c2689cef48cb" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY220<br>             <b>Area:</b> Al Amwaj<br>             <b>Orders:</b> 3<br>             <b>Distance:</b> 372.2 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_d87740dccadb4440c1edbfcb8c028a67.setContent(html_fdf6801a366bce7b41b7c2689cef48cb);
            
        

        circle_marker_3793854e378044d2e130223b793fe1f1.bindPopup(popup_d87740dccadb4440c1edbfcb8c028a67)
        ;

        
    
    
            var circle_marker_d6f8d9ee28fe1fb253ca8b7447194899 = L.circleMarker(
                [26.112165217391304, 50.14579565217391],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_006491baaf683ab55ea5e263204ac1b5 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_1015603b0430903c3b0ed1f7cd05022c = $(`<div id="html_1015603b0430903c3b0ed1f7cd05022c" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY221<br>             <b>Area:</b> Al Aqiq<br>             <b>Orders:</b> 23<br>             <b>Distance:</b> 371.5 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_006491baaf683ab55ea5e263204ac1b5.setContent(html_1015603b0430903c3b0ed1f7cd05022c);
            
        

        circle_marker_d6f8d9ee28fe1fb253ca8b7447194899.bindPopup(popup_006491baaf683ab55ea5e263204ac1b5)
        ;

        
    
    
            var circle_marker_fdd66ce09e10c508db64ca773c25676a = L.circleMarker(
                [26.125307894736842, 50.13860789473684],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_32d148e351071ce0f8ae5a3be070c5d5 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_1794d5f0f5579219f26e03bcf8d3408a = $(`<div id="html_1794d5f0f5579219f26e03bcf8d3408a" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY222<br>             <b>Area:</b> Al Amwaj<br>             <b>Orders:</b> 38<br>             <b>Distance:</b> 371.4 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_32d148e351071ce0f8ae5a3be070c5d5.setContent(html_1794d5f0f5579219f26e03bcf8d3408a);
            
        

        circle_marker_fdd66ce09e10c508db64ca773c25676a.bindPopup(popup_32d148e351071ce0f8ae5a3be070c5d5)
        ;

        
    
    
            var circle_marker_f83bbe1fd1560d6f0b4014e4112e830b = L.circleMarker(
                [26.15300909090909, 50.13271515151515],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_75e12f497102f12189fc6f09d6333414 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_5b653db71c45bd028483989d5c8af9d8 = $(`<div id="html_5b653db71c45bd028483989d5c8af9d8" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY224<br>             <b>Area:</b> Al Kawthar<br>             <b>Orders:</b> 33<br>             <b>Distance:</b> 372.2 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_75e12f497102f12189fc6f09d6333414.setContent(html_5b653db71c45bd028483989d5c8af9d8);
            
        

        circle_marker_f83bbe1fd1560d6f0b4014e4112e830b.bindPopup(popup_75e12f497102f12189fc6f09d6333414)
        ;

        
    
    
            var circle_marker_dce8bc2847e089a6e163ebb4320b7b08 = L.circleMarker(
                [26.17467333333333, 50.154626666666665],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_ffa5f7a181886e15aa30dbd6f56d8b61 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_cf314d836558ca2b17bd52ce96782dd3 = $(`<div id="html_cf314d836558ca2b17bd52ce96782dd3" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY225<br>             <b>Area:</b> Al Sheraa<br>             <b>Orders:</b> 30<br>             <b>Distance:</b> 375.2 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_ffa5f7a181886e15aa30dbd6f56d8b61.setContent(html_cf314d836558ca2b17bd52ce96782dd3);
            
        

        circle_marker_dce8bc2847e089a6e163ebb4320b7b08.bindPopup(popup_ffa5f7a181886e15aa30dbd6f56d8b61)
        ;

        
    
    
            var circle_marker_ee4d2e6fecfe1d3d9b774f7eb79f48f8 = L.circleMarker(
                [26.185006172839504, 50.16232839506173],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_af7be0ddb09033a305e5dac720cf2093 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_98b9e96835cb819bb53d54b9d02ff01c = $(`<div id="html_98b9e96835cb819bb53d54b9d02ff01c" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY226<br>             <b>Area:</b> حي الصواري<br>             <b>Orders:</b> 81<br>             <b>Distance:</b> 376.4 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_af7be0ddb09033a305e5dac720cf2093.setContent(html_98b9e96835cb819bb53d54b9d02ff01c);
            
        

        circle_marker_ee4d2e6fecfe1d3d9b774f7eb79f48f8.bindPopup(popup_af7be0ddb09033a305e5dac720cf2093)
        ;

        
    
    
            var circle_marker_2a70747f7799e69c5d8f1871a572abba = L.circleMarker(
                [26.19842777777778, 50.15519444444445],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_094657b376ba51c7a8289969f78184c2 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_084d55fd7a3f2c7e0b3c4d2c4ca35b7f = $(`<div id="html_084d55fd7a3f2c7e0b3c4d2c4ca35b7f" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY228<br>             <b>Area:</b> Al Sawari<br>             <b>Orders:</b> 18<br>             <b>Distance:</b> 376.4 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_094657b376ba51c7a8289969f78184c2.setContent(html_084d55fd7a3f2c7e0b3c4d2c4ca35b7f);
            
        

        circle_marker_2a70747f7799e69c5d8f1871a572abba.bindPopup(popup_094657b376ba51c7a8289969f78184c2)
        ;

        
    
    
            var circle_marker_436fb78f513002a3f67bd0435d2f7ffd = L.circleMarker(
                [24.770071875, 46.67594453125],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_60e324d27675df829f75218b50c62a56 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_8c02dffe606bb5333434c3a70eb6e194 = $(`<div id="html_8c02dffe606bb5333434c3a70eb6e194" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY23<br>             <b>Area:</b> Almasiaf<br>             <b>Orders:</b> 128<br>             <b>Distance:</b> 15.5 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_60e324d27675df829f75218b50c62a56.setContent(html_8c02dffe606bb5333434c3a70eb6e194);
            
        

        circle_marker_436fb78f513002a3f67bd0435d2f7ffd.bindPopup(popup_60e324d27675df829f75218b50c62a56)
        ;

        
    
    
            var circle_marker_6e1db668d7661f3179677006403722d1 = L.circleMarker(
                [26.216071232876715, 50.19746301369863],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_998de5ffd0db44d3d555a2c863417e99 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_65af5cf02851962803fdc0872ef4cd85 = $(`<div id="html_65af5cf02851962803fdc0872ef4cd85" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY230<br>             <b>Area:</b> حي الجسر<br>             <b>Orders:</b> 73<br>             <b>Distance:</b> 381.1 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_998de5ffd0db44d3d555a2c863417e99.setContent(html_65af5cf02851962803fdc0872ef4cd85);
            
        

        circle_marker_6e1db668d7661f3179677006403722d1.bindPopup(popup_998de5ffd0db44d3d555a2c863417e99)
        ;

        
    
    
            var circle_marker_ad280fea7083a35225878be74977a3ee = L.circleMarker(
                [26.246071739130432, 50.204783695652175],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_82e4dfc6b0c0467933986268ff678ec1 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_186800112fc98b5e95490a7e5ca4ba2d = $(`<div id="html_186800112fc98b5e95490a7e5ca4ba2d" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY231<br>             <b>Area:</b> Al Thawn<br>             <b>Orders:</b> 92<br>             <b>Distance:</b> 383.2 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_82e4dfc6b0c0467933986268ff678ec1.setContent(html_186800112fc98b5e95490a7e5ca4ba2d);
            
        

        circle_marker_ad280fea7083a35225878be74977a3ee.bindPopup(popup_82e4dfc6b0c0467933986268ff678ec1)
        ;

        
    
    
            var circle_marker_ee6541870fd6089bab747b2ad596463d = L.circleMarker(
                [26.280505769230768, 50.19700769230769],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_81a4a146983aac8ef1f1a9ba7e5b48cb = L.popup({
  "maxWidth": 200,
});

        
            
                var html_026f348e0b3049bac174e86bb2a0ce8d = $(`<div id="html_026f348e0b3049bac174e86bb2a0ce8d" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY232<br>             <b>Area:</b> حي الثقبة<br>             <b>Orders:</b> 52<br>             <b>Distance:</b> 384.2 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_81a4a146983aac8ef1f1a9ba7e5b48cb.setContent(html_026f348e0b3049bac174e86bb2a0ce8d);
            
        

        circle_marker_ee6541870fd6089bab747b2ad596463d.bindPopup(popup_81a4a146983aac8ef1f1a9ba7e5b48cb)
        ;

        
    
    
            var circle_marker_e2e109d90894ee0093a83032594c6399 = L.circleMarker(
                [26.30406, 50.21027],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_6bd939247b9d5cce27461a3d74c5d861 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_7985ee11669c8f0325ae0f18218faa33 = $(`<div id="html_7985ee11669c8f0325ae0f18218faa33" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY233<br>             <b>Area:</b> Al Bandariyah<br>             <b>Orders:</b> 20<br>             <b>Distance:</b> 386.5 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_6bd939247b9d5cce27461a3d74c5d861.setContent(html_7985ee11669c8f0325ae0f18218faa33);
            
        

        circle_marker_e2e109d90894ee0093a83032594c6399.bindPopup(popup_6bd939247b9d5cce27461a3d74c5d861)
        ;

        
    
    
            var circle_marker_29a38b91671e0c1e4f80eaa575eeb130 = L.circleMarker(
                [26.313168, 50.180704],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_15c4c08c407d55084279944c7241e6a8 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_b2c7e4d1732f5d1ccac6e75e33758c3c = $(`<div id="html_b2c7e4d1732f5d1ccac6e75e33758c3c" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY234<br>             <b>Area:</b> حي العليا<br>             <b>Orders:</b> 50<br>             <b>Distance:</b> 384.4 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_15c4c08c407d55084279944c7241e6a8.setContent(html_b2c7e4d1732f5d1ccac6e75e33758c3c);
            
        

        circle_marker_29a38b91671e0c1e4f80eaa575eeb130.bindPopup(popup_15c4c08c407d55084279944c7241e6a8)
        ;

        
    
    
            var circle_marker_724bf9abca825c55f3f03b1422efcf70 = L.circleMarker(
                [26.317718181818183, 50.215495454545454],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_7b7f16a40e98ce13bc424f63c15677ef = L.popup({
  "maxWidth": 200,
});

        
            
                var html_caf5aa09eedcc2047629e00f62f39b67 = $(`<div id="html_caf5aa09eedcc2047629e00f62f39b67" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY235<br>             <b>Area:</b> حي البستان<br>             <b>Orders:</b> 22<br>             <b>Distance:</b> 387.7 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_7b7f16a40e98ce13bc424f63c15677ef.setContent(html_caf5aa09eedcc2047629e00f62f39b67);
            
        

        circle_marker_724bf9abca825c55f3f03b1422efcf70.bindPopup(popup_7b7f16a40e98ce13bc424f63c15677ef)
        ;

        
    
    
            var circle_marker_af1698c06692f1aed0f7a06963f23bae = L.circleMarker(
                [26.341028571428573, 50.178961904761906],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_4b88f164bc8375fbde04202b53ca3b91 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_1822ab99af096926326ad5f481f4f1d0 = $(`<div id="html_1822ab99af096926326ad5f481f4f1d0" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY236<br>             <b>Area:</b> Al Dawhah Al Janubiyah<br>             <b>Orders:</b> 21<br>             <b>Distance:</b> 385.7 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_4b88f164bc8375fbde04202b53ca3b91.setContent(html_1822ab99af096926326ad5f481f4f1d0);
            
        

        circle_marker_af1698c06692f1aed0f7a06963f23bae.bindPopup(popup_4b88f164bc8375fbde04202b53ca3b91)
        ;

        
    
    
            var circle_marker_43332e11ec0662a9fe9a06c0b4274454 = L.circleMarker(
                [26.343700000000002, 50.2004],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_cbc05b010028b9118af22d209ba4360b = L.popup({
  "maxWidth": 200,
});

        
            
                var html_dc3bd7d14a24e9be046d19c03a2f1640 = $(`<div id="html_dc3bd7d14a24e9be046d19c03a2f1640" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY237<br>             <b>Area:</b> Al Rakah Al Janubiyah<br>             <b>Orders:</b> 6<br>             <b>Distance:</b> 387.7 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_cbc05b010028b9118af22d209ba4360b.setContent(html_dc3bd7d14a24e9be046d19c03a2f1640);
            
        

        circle_marker_43332e11ec0662a9fe9a06c0b4274454.bindPopup(popup_cbc05b010028b9118af22d209ba4360b)
        ;

        
    
    
            var circle_marker_436d6311b4d9067c7c2fac2e1cc3b61f = L.circleMarker(
                [26.355549999999997, 49.98255],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_a173f974b557341df2cbe87d7756e577 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_3e468524dadef82a8b5294f00ab6a39a = $(`<div id="html_3e468524dadef82a8b5294f00ab6a39a" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY238<br>             <b>Area:</b> حي الإسكان الجنوبي<br>             <b>Orders:</b> 12<br>             <b>Distance:</b> 369.2 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_a173f974b557341df2cbe87d7756e577.setContent(html_3e468524dadef82a8b5294f00ab6a39a);
            
        

        circle_marker_436d6311b4d9067c7c2fac2e1cc3b61f.bindPopup(popup_a173f974b557341df2cbe87d7756e577)
        ;

        
    
    
            var circle_marker_5a4eb9d14f6f59f374e0a770205daa49 = L.circleMarker(
                [24.74575125, 46.66605375],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_38d3a295abfb5e781fe0aa652214382b = L.popup({
  "maxWidth": 200,
});

        
            
                var html_21e25aad2aa976fdcd594050ef884df0 = $(`<div id="html_21e25aad2aa976fdcd594050ef884df0" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY24<br>             <b>Area:</b> حي الملك فهد<br>             <b>Orders:</b> 80<br>             <b>Distance:</b> 15.0 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_38d3a295abfb5e781fe0aa652214382b.setContent(html_21e25aad2aa976fdcd594050ef884df0);
            
        

        circle_marker_5a4eb9d14f6f59f374e0a770205daa49.bindPopup(popup_38d3a295abfb5e781fe0aa652214382b)
        ;

        
    
    
            var circle_marker_8a932fa14b42a68c6683a344879a1fea = L.circleMarker(
                [26.413404761904765, 50.00816666666666],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_35ceb5d73c2b287acc32cb293bd2d51c = L.popup({
  "maxWidth": 200,
});

        
            
                var html_3804516dffbb79a89bc3451601a1cbcd = $(`<div id="html_3804516dffbb79a89bc3451601a1cbcd" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY240<br>             <b>Area:</b> An Nur<br>             <b>Orders:</b> 21<br>             <b>Distance:</b> 374.7 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_35ceb5d73c2b287acc32cb293bd2d51c.setContent(html_3804516dffbb79a89bc3451601a1cbcd);
            
        

        circle_marker_8a932fa14b42a68c6683a344879a1fea.bindPopup(popup_35ceb5d73c2b287acc32cb293bd2d51c)
        ;

        
    
    
            var circle_marker_2f9dbbbc91a45bfdf86779f30ccf0533 = L.circleMarker(
                [26.41137435897436, 50.049091025641026],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_caf5be1a4e74fbe4b8ba00520c8acb7f = L.popup({
  "maxWidth": 200,
});

        
            
                var html_2920bdca66364b613c4e407a77b20926 = $(`<div id="html_2920bdca66364b613c4e407a77b20926" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY242<br>             <b>Area:</b> حي احد<br>             <b>Orders:</b> 156<br>             <b>Distance:</b> 378.1 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_caf5be1a4e74fbe4b8ba00520c8acb7f.setContent(html_2920bdca66364b613c4e407a77b20926);
            
        

        circle_marker_2f9dbbbc91a45bfdf86779f30ccf0533.bindPopup(popup_caf5be1a4e74fbe4b8ba00520c8acb7f)
        ;

        
    
    
            var circle_marker_c21f713d3fd24e6e4f46e306314da39b = L.circleMarker(
                [26.393632692307694, 50.05492980769231],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_737d9fecaacd5529f8465ca312c59cc4 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_27cc5729f412171008bef2053f9cf4cb = $(`<div id="html_27cc5729f412171008bef2053f9cf4cb" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY243<br>             <b>Area:</b> حي احد<br>             <b>Orders:</b> 208<br>             <b>Distance:</b> 377.6 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_737d9fecaacd5529f8465ca312c59cc4.setContent(html_27cc5729f412171008bef2053f9cf4cb);
            
        

        circle_marker_c21f713d3fd24e6e4f46e306314da39b.bindPopup(popup_737d9fecaacd5529f8465ca312c59cc4)
        ;

        
    
    
            var circle_marker_ac02a7e71bd510752930e55da2aa61bb = L.circleMarker(
                [26.358948, 50.0337344],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_1adf3e88c8604949e9722684737a3c6a = L.popup({
  "maxWidth": 200,
});

        
            
                var html_518f76829a84970f5883ab52d8ea9007 = $(`<div id="html_518f76829a84970f5883ab52d8ea9007" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY244<br>             <b>Area:</b> حي المنار<br>             <b>Orders:</b> 125<br>             <b>Distance:</b> 373.9 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_1adf3e88c8604949e9722684737a3c6a.setContent(html_518f76829a84970f5883ab52d8ea9007);
            
        

        circle_marker_ac02a7e71bd510752930e55da2aa61bb.bindPopup(popup_1adf3e88c8604949e9722684737a3c6a)
        ;

        
    
    
            var circle_marker_ca99914d2c27a14c5ff91914713dd72a = L.circleMarker(
                [26.363769888475836, 50.06148921933086],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_82b9c1661c4eaeed189e72432b047563 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_9e63a779a5eb802944a9dfa800ba96e5 = $(`<div id="html_9e63a779a5eb802944a9dfa800ba96e5" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY245<br>             <b>Area:</b> حي طيبة<br>             <b>Orders:</b> 269<br>             <b>Distance:</b> 376.6 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_82b9c1661c4eaeed189e72432b047563.setContent(html_9e63a779a5eb802944a9dfa800ba96e5);
            
        

        circle_marker_ca99914d2c27a14c5ff91914713dd72a.bindPopup(popup_82b9c1661c4eaeed189e72432b047563)
        ;

        
    
    
            var circle_marker_35ec9cd753d396304692c08697a8e5c5 = L.circleMarker(
                [26.450184705882354, 50.074885882352945],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_c87a6caf467e8e60a9dab76d4f8ab758 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_56d9be1676ff5ca4709f3ea28e5f6317 = $(`<div id="html_56d9be1676ff5ca4709f3ea28e5f6317" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY246<br>             <b>Area:</b> حي العنود<br>             <b>Orders:</b> 85<br>             <b>Distance:</b> 382.5 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_c87a6caf467e8e60a9dab76d4f8ab758.setContent(html_56d9be1676ff5ca4709f3ea28e5f6317);
            
        

        circle_marker_35ec9cd753d396304692c08697a8e5c5.bindPopup(popup_c87a6caf467e8e60a9dab76d4f8ab758)
        ;

        
    
    
            var circle_marker_88aad663f75e3a10f99cf0efb6247b31 = L.circleMarker(
                [26.460791304347826, 50.092652173913045],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_b4ba4973a7c0c21ed9bc58687c9e1e57 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_a6aa0e6cd32c18e2dc3552b5d1075628 = $(`<div id="html_a6aa0e6cd32c18e2dc3552b5d1075628" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY247<br>             <b>Area:</b> حي الزهور<br>             <b>Orders:</b> 23<br>             <b>Distance:</b> 384.6 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_b4ba4973a7c0c21ed9bc58687c9e1e57.setContent(html_a6aa0e6cd32c18e2dc3552b5d1075628);
            
        

        circle_marker_88aad663f75e3a10f99cf0efb6247b31.bindPopup(popup_b4ba4973a7c0c21ed9bc58687c9e1e57)
        ;

        
    
    
            var circle_marker_ed82dac07c6ef56f36fab0c84f5f0392 = L.circleMarker(
                [26.45823894736842, 50.12459157894737],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_74fdc080b28b543fc9a26b9c23458c28 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_152de65bc34cc7d2c7eca97366baf3b8 = $(`<div id="html_152de65bc34cc7d2c7eca97366baf3b8" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY248<br>             <b>Area:</b> حي المزروعية<br>             <b>Orders:</b> 95<br>             <b>Distance:</b> 387.2 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_74fdc080b28b543fc9a26b9c23458c28.setContent(html_152de65bc34cc7d2c7eca97366baf3b8);
            
        

        circle_marker_ed82dac07c6ef56f36fab0c84f5f0392.bindPopup(popup_74fdc080b28b543fc9a26b9c23458c28)
        ;

        
    
    
            var circle_marker_1118b9d8cd06a6ed7d25e5484a28403e = L.circleMarker(
                [26.443510526315787, 50.12181052631579],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_0c29435df11d77f537f538fb994b8922 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_e859bfc206861101ab8f5fc305df1f1a = $(`<div id="html_e859bfc206861101ab8f5fc305df1f1a" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY249<br>             <b>Area:</b> حي المزروعية<br>             <b>Orders:</b> 19<br>             <b>Distance:</b> 386.1 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_0c29435df11d77f537f538fb994b8922.setContent(html_e859bfc206861101ab8f5fc305df1f1a);
            
        

        circle_marker_1118b9d8cd06a6ed7d25e5484a28403e.bindPopup(popup_0c29435df11d77f537f538fb994b8922)
        ;

        
    
    
            var circle_marker_e6334c233b93bbb46ed5af1ea6354903 = L.circleMarker(
                [26.428943848580445, 50.088458044164035],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_8cc370bb677ef7c493dfe0a68fa665e3 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_798b89f2d03722228338fb44bf74083f = $(`<div id="html_798b89f2d03722228338fb44bf74083f" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY250<br>             <b>Area:</b> حي الاتصالات<br>             <b>Orders:</b> 317<br>             <b>Distance:</b> 382.4 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_8cc370bb677ef7c493dfe0a68fa665e3.setContent(html_798b89f2d03722228338fb44bf74083f);
            
        

        circle_marker_e6334c233b93bbb46ed5af1ea6354903.bindPopup(popup_8cc370bb677ef7c493dfe0a68fa665e3)
        ;

        
    
    
            var circle_marker_321b4db62cf029764d865f330c27fdb3 = L.circleMarker(
                [26.387642916666668, 50.0933525],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_2fd4f3b77500bbb5a632a4eedddbe737 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_9ed9dbbaa5873a636f4a327f6fe0bb0d = $(`<div id="html_9ed9dbbaa5873a636f4a327f6fe0bb0d" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY251<br>             <b>Area:</b> حي الروضة<br>             <b>Orders:</b> 240<br>             <b>Distance:</b> 380.6 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_2fd4f3b77500bbb5a632a4eedddbe737.setContent(html_9ed9dbbaa5873a636f4a327f6fe0bb0d);
            
        

        circle_marker_321b4db62cf029764d865f330c27fdb3.bindPopup(popup_2fd4f3b77500bbb5a632a4eedddbe737)
        ;

        
    
    
            var circle_marker_38c1648f51e412c046889f7f8f89b45b = L.circleMarker(
                [26.403467213114755, 50.123455737704916],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_728722bfa98bbf8b34c3a6f327185576 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_d1b8c59cd72d0fa9bc37fa64e73c20c9 = $(`<div id="html_d1b8c59cd72d0fa9bc37fa64e73c20c9" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY252<br>             <b>Area:</b> حي الشفاء<br>             <b>Orders:</b> 61<br>             <b>Distance:</b> 384.1 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_728722bfa98bbf8b34c3a6f327185576.setContent(html_d1b8c59cd72d0fa9bc37fa64e73c20c9);
            
        

        circle_marker_38c1648f51e412c046889f7f8f89b45b.bindPopup(popup_728722bfa98bbf8b34c3a6f327185576)
        ;

        
    
    
            var circle_marker_420c88c582f7325b57e29a4fbbd5d8e1 = L.circleMarker(
                [26.39864516129032, 50.133922580645155],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_3855a90bdad8aec769d42a5ef07a4887 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_4ee97a6a2e300a54eb901a6ec1766f2f = $(`<div id="html_4ee97a6a2e300a54eb901a6ec1766f2f" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY253<br>             <b>Area:</b> حي المنتزه<br>             <b>Orders:</b> 31<br>             <b>Distance:</b> 384.7 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_3855a90bdad8aec769d42a5ef07a4887.setContent(html_4ee97a6a2e300a54eb901a6ec1766f2f);
            
        

        circle_marker_420c88c582f7325b57e29a4fbbd5d8e1.bindPopup(popup_3855a90bdad8aec769d42a5ef07a4887)
        ;

        
    
    
            var circle_marker_163940df3c8565f9e5ac49b87834585b = L.circleMarker(
                [26.373756521739132, 50.17401304347827],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_9ee11daac477d3d087683c8f206ebc4c = L.popup({
  "maxWidth": 200,
});

        
            
                var html_7c0b03f9f218d37ee05a428b863781ca = $(`<div id="html_7c0b03f9f218d37ee05a428b863781ca" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY254<br>             <b>Area:</b> حي الراكة الشمالية<br>             <b>Orders:</b> 23<br>             <b>Distance:</b> 386.9 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_9ee11daac477d3d087683c8f206ebc4c.setContent(html_7c0b03f9f218d37ee05a428b863781ca);
            
        

        circle_marker_163940df3c8565f9e5ac49b87834585b.bindPopup(popup_9ee11daac477d3d087683c8f206ebc4c)
        ;

        
    
    
            var circle_marker_1ded5c0293219e375c00e5939dcbb2af = L.circleMarker(
                [26.386164444444447, 50.19054222222222],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_b90d9878c7ec7818deeabf42b00d9fa4 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_c93686a57cfb9bb39e4b952ebd0fe45e = $(`<div id="html_c93686a57cfb9bb39e4b952ebd0fe45e" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY255<br>             <b>Area:</b> حي النورس<br>             <b>Orders:</b> 45<br>             <b>Distance:</b> 389.0 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_b90d9878c7ec7818deeabf42b00d9fa4.setContent(html_c93686a57cfb9bb39e4b952ebd0fe45e);
            
        

        circle_marker_1ded5c0293219e375c00e5939dcbb2af.bindPopup(popup_b90d9878c7ec7818deeabf42b00d9fa4)
        ;

        
    
    
            var circle_marker_c19dbbfcb57028996ca0e6bb8e2db81b = L.circleMarker(
                [26.39622105263158, 50.012040080971666],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_96e646c16af63068462d5416a7640504 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_ed1c65a49313f3fe0f30b68777a3012d = $(`<div id="html_ed1c65a49313f3fe0f30b68777a3012d" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY256<br>             <b>Area:</b> حي بدر<br>             <b>Orders:</b> 494<br>             <b>Distance:</b> 374.0 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_96e646c16af63068462d5416a7640504.setContent(html_ed1c65a49313f3fe0f30b68777a3012d);
            
        

        circle_marker_c19dbbfcb57028996ca0e6bb8e2db81b.bindPopup(popup_96e646c16af63068462d5416a7640504)
        ;

        
    
    
            var circle_marker_acca7bfac62a2e05c0045f0d21a86485 = L.circleMarker(
                [26.466810869565215, 50.08092173913044],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_25901437186d3b24dddbc56f3499b7fc = L.popup({
  "maxWidth": 200,
});

        
            
                var html_2848f2f93d112f15f2de899400e6ca08 = $(`<div id="html_2848f2f93d112f15f2de899400e6ca08" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY257<br>             <b>Area:</b> حي الحمراء<br>             <b>Orders:</b> 46<br>             <b>Distance:</b> 383.9 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_25901437186d3b24dddbc56f3499b7fc.setContent(html_2848f2f93d112f15f2de899400e6ca08);
            
        

        circle_marker_acca7bfac62a2e05c0045f0d21a86485.bindPopup(popup_25901437186d3b24dddbc56f3499b7fc)
        ;

        
    
    
            var circle_marker_e1472a2a0afd3d3e165e32c15d5ad049 = L.circleMarker(
                [26.4620171875, 50.0596171875],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_d2096b1008ed198414838296f4326681 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_2c6b23e259830337d5f1681d2e140d84 = $(`<div id="html_2c6b23e259830337d5f1681d2e140d84" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY258<br>             <b>Area:</b> حي العزيزية<br>             <b>Orders:</b> 64<br>             <b>Distance:</b> 381.8 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_d2096b1008ed198414838296f4326681.setContent(html_2c6b23e259830337d5f1681d2e140d84);
            
        

        circle_marker_e1472a2a0afd3d3e165e32c15d5ad049.bindPopup(popup_d2096b1008ed198414838296f4326681)
        ;

        
    
    
            var circle_marker_429085c21361fd277d5d33c81d493090 = L.circleMarker(
                [26.44130875, 50.0371825],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_13fc87e222c0e28d40e915dea84028cc = L.popup({
  "maxWidth": 200,
});

        
            
                var html_0a450b577fac02f823d138e048ed32a2 = $(`<div id="html_0a450b577fac02f823d138e048ed32a2" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY259<br>             <b>Area:</b> حي النور<br>             <b>Orders:</b> 80<br>             <b>Distance:</b> 378.7 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_13fc87e222c0e28d40e915dea84028cc.setContent(html_0a450b577fac02f823d138e048ed32a2);
            
        

        circle_marker_429085c21361fd277d5d33c81d493090.bindPopup(popup_13fc87e222c0e28d40e915dea84028cc)
        ;

        
    
    
            var circle_marker_56c4580fc4f2dd97183f2408336c11b1 = L.circleMarker(
                [24.683752112676057, 46.58737816901408],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_b792b044f7df6ee90b53274b95c5f289 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_cfdab5135ca7958f84c3da49ba517f9d = $(`<div id="html_cfdab5135ca7958f84c3da49ba517f9d" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY26<br>             <b>Area:</b> 'Irqah<br>             <b>Orders:</b> 142<br>             <b>Distance:</b> 21.5 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_b792b044f7df6ee90b53274b95c5f289.setContent(html_cfdab5135ca7958f84c3da49ba517f9d);
            
        

        circle_marker_56c4580fc4f2dd97183f2408336c11b1.bindPopup(popup_b792b044f7df6ee90b53274b95c5f289)
        ;

        
    
    
            var circle_marker_f24b6a599ca4aff012630fd2d5b1ff3b = L.circleMarker(
                [26.420204225352112, 50.01216408450705],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_ff0c4a3b19b7cc0bebcab0de52d9c614 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_71221fa3aef5c8fd1213b11980f76c23 = $(`<div id="html_71221fa3aef5c8fd1213b11980f76c23" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY260<br>             <b>Area:</b> حي النور<br>             <b>Orders:</b> 142<br>             <b>Distance:</b> 375.4 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_ff0c4a3b19b7cc0bebcab0de52d9c614.setContent(html_71221fa3aef5c8fd1213b11980f76c23);
            
        

        circle_marker_f24b6a599ca4aff012630fd2d5b1ff3b.bindPopup(popup_ff0c4a3b19b7cc0bebcab0de52d9c614)
        ;

        
    
    
            var circle_marker_3c8d761b9353a9ddf2cca65c07b79086 = L.circleMarker(
                [26.477053658536583, 50.05598048780488],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_1798ef9d667509f46861d3eb66a70c7c = L.popup({
  "maxWidth": 200,
});

        
            
                var html_292f61b5406cb6bdf06bc4b47480a794 = $(`<div id="html_292f61b5406cb6bdf06bc4b47480a794" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY261<br>             <b>Area:</b> حي الكوثر<br>             <b>Orders:</b> 41<br>             <b>Distance:</b> 382.3 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_1798ef9d667509f46861d3eb66a70c7c.setContent(html_292f61b5406cb6bdf06bc4b47480a794);
            
        

        circle_marker_3c8d761b9353a9ddf2cca65c07b79086.bindPopup(popup_1798ef9d667509f46861d3eb66a70c7c)
        ;

        
    
    
            var circle_marker_0238d5e0a30df46e02201efab872937e = L.circleMarker(
                [26.478667741935485, 50.045470967741934],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_6b383b6274c3ad6447dcc81673bf388e = L.popup({
  "maxWidth": 200,
});

        
            
                var html_fe3f1f1fba8905d17540c96a4e9e0f00 = $(`<div id="html_fe3f1f1fba8905d17540c96a4e9e0f00" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY262<br>             <b>Area:</b> حي الكوثر<br>             <b>Orders:</b> 31<br>             <b>Distance:</b> 381.5 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_6b383b6274c3ad6447dcc81673bf388e.setContent(html_fe3f1f1fba8905d17540c96a4e9e0f00);
            
        

        circle_marker_0238d5e0a30df46e02201efab872937e.bindPopup(popup_6b383b6274c3ad6447dcc81673bf388e)
        ;

        
    
    
            var circle_marker_2afb0e5ac684451eb5c16c3774a1f583 = L.circleMarker(
                [26.466111904761906, 50.029999999999994],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_a25c8c77ee23c4977bb80696d65e2379 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_a821f2442cb966d5fe10640b2e30217e = $(`<div id="html_a821f2442cb966d5fe10640b2e30217e" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY263<br>             <b>Area:</b> حي الخليج<br>             <b>Orders:</b> 42<br>             <b>Distance:</b> 379.5 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_a25c8c77ee23c4977bb80696d65e2379.setContent(html_a821f2442cb966d5fe10640b2e30217e);
            
        

        circle_marker_2afb0e5ac684451eb5c16c3774a1f583.bindPopup(popup_a25c8c77ee23c4977bb80696d65e2379)
        ;

        
    
    
            var circle_marker_af7bda33cc7e55e197195cdd9106d279 = L.circleMarker(
                [26.428913333333334, 50.00220933333333],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_f0a9f7d1852e90151b592b18a20d3d63 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_7426b137c158a7cb9aecee75d2f6ab56 = $(`<div id="html_7426b137c158a7cb9aecee75d2f6ab56" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY264<br>             <b>Area:</b> King Fahd Suburb<br>             <b>Orders:</b> 75<br>             <b>Distance:</b> 375.0 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_f0a9f7d1852e90151b592b18a20d3d63.setContent(html_7426b137c158a7cb9aecee75d2f6ab56);
            
        

        circle_marker_af7bda33cc7e55e197195cdd9106d279.bindPopup(popup_f0a9f7d1852e90151b592b18a20d3d63)
        ;

        
    
    
            var circle_marker_fb39f8edea5e7eba8c79cc913f2110f2 = L.circleMarker(
                [26.497422222222223, 50.041044444444445],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_746489c970b37d206b2b65149b88885b = L.popup({
  "maxWidth": 200,
});

        
            
                var html_0bffd238e803779e2bcd072ab1ffbb4b = $(`<div id="html_0bffd238e803779e2bcd072ab1ffbb4b" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY265<br>             <b>Area:</b> Al Ghadir<br>             <b>Orders:</b> 9<br>             <b>Distance:</b> 382.2 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_746489c970b37d206b2b65149b88885b.setContent(html_0bffd238e803779e2bcd072ab1ffbb4b);
            
        

        circle_marker_fb39f8edea5e7eba8c79cc913f2110f2.bindPopup(popup_746489c970b37d206b2b65149b88885b)
        ;

        
    
    
            var circle_marker_79cc38f6d1a0f9d59c616eef1db2b1dd = L.circleMarker(
                [26.505811904761902, 50.01060952380952],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_2e3f9663e3f3ed94c18a0b7d5c592764 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_27d2cf3ad62286471eab409d5d8534e3 = $(`<div id="html_27d2cf3ad62286471eab409d5d8534e3" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY267<br>             <b>Area:</b> حي قرطبة<br>             <b>Orders:</b> 42<br>             <b>Distance:</b> 380.1 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_2e3f9663e3f3ed94c18a0b7d5c592764.setContent(html_27d2cf3ad62286471eab409d5d8534e3);
            
        

        circle_marker_79cc38f6d1a0f9d59c616eef1db2b1dd.bindPopup(popup_2e3f9663e3f3ed94c18a0b7d5c592764)
        ;

        
    
    
            var circle_marker_3fb9de66b3537f4700c63939cbecf789 = L.circleMarker(
                [26.451295555555557, 49.989970222222226],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_983ef3bee215565b09a58ae718efaa28 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_a1e83f35435c18879860ed4f787c7b95 = $(`<div id="html_a1e83f35435c18879860ed4f787c7b95" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY268<br>             <b>Area:</b> حي ضاحية الملك فهد<br>             <b>Orders:</b> 225<br>             <b>Distance:</b> 375.2 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_983ef3bee215565b09a58ae718efaa28.setContent(html_a1e83f35435c18879860ed4f787c7b95);
            
        

        circle_marker_3fb9de66b3537f4700c63939cbecf789.bindPopup(popup_983ef3bee215565b09a58ae718efaa28)
        ;

        
    
    
            var circle_marker_f02355982b48aa0b63f1845c16bbeecf = L.circleMarker(
                [26.52314905660377, 50.02437924528302],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_99f2c42f593480f79dba65549c1aa14e = L.popup({
  "maxWidth": 200,
});

        
            
                var html_9e5b3b6f369e7ccbf5b5f35ae4dcc2fd = $(`<div id="html_9e5b3b6f369e7ccbf5b5f35ae4dcc2fd" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY269<br>             <b>Area:</b> حي عنك<br>             <b>Orders:</b> 53<br>             <b>Distance:</b> 382.3 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_99f2c42f593480f79dba65549c1aa14e.setContent(html_9e5b3b6f369e7ccbf5b5f35ae4dcc2fd);
            
        

        circle_marker_f02355982b48aa0b63f1845c16bbeecf.bindPopup(popup_99f2c42f593480f79dba65549c1aa14e)
        ;

        
    
    
            var circle_marker_9c94244f66b2f5dfb6cde704983bec22 = L.circleMarker(
                [24.641883056478406, 46.56257707641196],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_cbabea464a69df82f381da3832d02702 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_be0796109db536e727ae5a5d1737cb97 = $(`<div id="html_be0796109db536e727ae5a5d1737cb97" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY27<br>             <b>Area:</b> حي ظهرة لبن<br>             <b>Orders:</b> 301<br>             <b>Distance:</b> 24.6 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_cbabea464a69df82f381da3832d02702.setContent(html_be0796109db536e727ae5a5d1737cb97);
            
        

        circle_marker_9c94244f66b2f5dfb6cde704983bec22.bindPopup(popup_cbabea464a69df82f381da3832d02702)
        ;

        
    
    
            var circle_marker_a5973d410e798dfdcba62cddbd2ca7fc = L.circleMarker(
                [26.52542380952381, 50.004890476190475],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_8601078f9e4fee43a96f8c2416ad065d = L.popup({
  "maxWidth": 200,
});

        
            
                var html_a596d7c3688174d549ec790f493200f9 = $(`<div id="html_a596d7c3688174d549ec790f493200f9" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY270<br>             <b>Area:</b> Umm Al Hamam<br>             <b>Orders:</b> 21<br>             <b>Distance:</b> 380.8 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_8601078f9e4fee43a96f8c2416ad065d.setContent(html_a596d7c3688174d549ec790f493200f9);
            
        

        circle_marker_a5973d410e798dfdcba62cddbd2ca7fc.bindPopup(popup_8601078f9e4fee43a96f8c2416ad065d)
        ;

        
    
    
            var circle_marker_79a78f85418936a16771e086398abca5 = L.circleMarker(
                [26.488150434782607, 50.00669652173913],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_4967798eb3c80bfda3bc31a72b2fe40e = L.popup({
  "maxWidth": 200,
});

        
            
                var html_17e64187fcc8b0e46836e77df6417973 = $(`<div id="html_17e64187fcc8b0e46836e77df6417973" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY271<br>             <b>Area:</b> حي عنك<br>             <b>Orders:</b> 115<br>             <b>Distance:</b> 378.8 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_4967798eb3c80bfda3bc31a72b2fe40e.setContent(html_17e64187fcc8b0e46836e77df6417973);
            
        

        circle_marker_79a78f85418936a16771e086398abca5.bindPopup(popup_4967798eb3c80bfda3bc31a72b2fe40e)
        ;

        
    
    
            var circle_marker_1421386130f0d36cc58c89fd815dd867 = L.circleMarker(
                [26.49535635359116, 50.01707845303868],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_2135cedd88540576c4e04318b66af60d = L.popup({
  "maxWidth": 200,
});

        
            
                var html_226762f13885c4ce79cd93058093f17f = $(`<div id="html_226762f13885c4ce79cd93058093f17f" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY272<br>             <b>Area:</b> حي عنك<br>             <b>Orders:</b> 181<br>             <b>Distance:</b> 380.1 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_2135cedd88540576c4e04318b66af60d.setContent(html_226762f13885c4ce79cd93058093f17f);
            
        

        circle_marker_1421386130f0d36cc58c89fd815dd867.bindPopup(popup_2135cedd88540576c4e04318b66af60d)
        ;

        
    
    
            var circle_marker_48b168dbc62c9c5930d71d7e04ffb54a = L.circleMarker(
                [26.349823809523812, 50.09111190476191],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_23a6850b6c9d4260666ef01a9bfa2e6c = L.popup({
  "maxWidth": 200,
});

        
            
                var html_3df6b49474ec6a7afdd63d0af02a0a1c = $(`<div id="html_3df6b49474ec6a7afdd63d0af02a0a1c" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY273<br>             <b>Area:</b> Ash Shulah<br>             <b>Orders:</b> 42<br>             <b>Distance:</b> 378.4 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_23a6850b6c9d4260666ef01a9bfa2e6c.setContent(html_3df6b49474ec6a7afdd63d0af02a0a1c);
            
        

        circle_marker_48b168dbc62c9c5930d71d7e04ffb54a.bindPopup(popup_23a6850b6c9d4260666ef01a9bfa2e6c)
        ;

        
    
    
            var circle_marker_40990e5e4e633b41a87ba21857542062 = L.circleMarker(
                [26.335, 50.1474695652174],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_637c36223b381f49916dcbe860995807 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_e550071a3ef96faa8150370d8a9c241f = $(`<div id="html_e550071a3ef96faa8150370d8a9c241f" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY274<br>             <b>Area:</b> Aljamiah District<br>             <b>Orders:</b> 23<br>             <b>Distance:</b> 382.6 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_637c36223b381f49916dcbe860995807.setContent(html_e550071a3ef96faa8150370d8a9c241f);
            
        

        circle_marker_40990e5e4e633b41a87ba21857542062.bindPopup(popup_637c36223b381f49916dcbe860995807)
        ;

        
    
    
            var circle_marker_776905118cf18826df3f51e8c6e3e92b = L.circleMarker(
                [26.310533333333336, 50.16846666666667],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_857a136bca7b714b7086a6dd846003e0 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_316a8d571c2922496355300dd1ec1668 = $(`<div id="html_316a8d571c2922496355300dd1ec1668" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY275<br>             <b>Area:</b> حي العليا<br>             <b>Orders:</b> 6<br>             <b>Distance:</b> 383.2 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_857a136bca7b714b7086a6dd846003e0.setContent(html_316a8d571c2922496355300dd1ec1668);
            
        

        circle_marker_776905118cf18826df3f51e8c6e3e92b.bindPopup(popup_857a136bca7b714b7086a6dd846003e0)
        ;

        
    
    
            var circle_marker_f2b4d58fd23b419e2d18ec45b6c46541 = L.circleMarker(
                [26.268715873015875, 50.19517777777777],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_0a2cc40d5ad0565f4390fdf34b199b95 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_6d6e9f854577b60fc3420d1b5579c304 = $(`<div id="html_6d6e9f854577b60fc3420d1b5579c304" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY276<br>             <b>Area:</b> حي الثقبة<br>             <b>Orders:</b> 63<br>             <b>Distance:</b> 383.4 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_0a2cc40d5ad0565f4390fdf34b199b95.setContent(html_6d6e9f854577b60fc3420d1b5579c304);
            
        

        circle_marker_f2b4d58fd23b419e2d18ec45b6c46541.bindPopup(popup_0a2cc40d5ad0565f4390fdf34b199b95)
        ;

        
    
    
            var circle_marker_1f0d9dcce5dea729b475be7820421d1c = L.circleMarker(
                [26.302965625, 50.164584375],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_bd9516e3a224a054b71170888f086186 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_904e90a87831e8bdb4a109bb1ee01b44 = $(`<div id="html_904e90a87831e8bdb4a109bb1ee01b44" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY277<br>             <b>Area:</b> حي العليا<br>             <b>Orders:</b> 32<br>             <b>Distance:</b> 382.4 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_bd9516e3a224a054b71170888f086186.setContent(html_904e90a87831e8bdb4a109bb1ee01b44);
            
        

        circle_marker_1f0d9dcce5dea729b475be7820421d1c.bindPopup(popup_bd9516e3a224a054b71170888f086186)
        ;

        
    
    
            var circle_marker_2679757c1f575fd4d5a6c461965cdd38 = L.circleMarker(
                [26.19809692307692, 50.19421692307692],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_e0e38305cf3f76fb12c9350bd5ccd80b = L.popup({
  "maxWidth": 200,
});

        
            
                var html_8dedeb4c281ca17bbb4575b179acce5e = $(`<div id="html_8dedeb4c281ca17bbb4575b179acce5e" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY278<br>             <b>Area:</b> حي التحلية<br>             <b>Orders:</b> 65<br>             <b>Distance:</b> 379.9 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_e0e38305cf3f76fb12c9350bd5ccd80b.setContent(html_8dedeb4c281ca17bbb4575b179acce5e);
            
        

        circle_marker_2679757c1f575fd4d5a6c461965cdd38.bindPopup(popup_e0e38305cf3f76fb12c9350bd5ccd80b)
        ;

        
    
    
            var circle_marker_09937309e6c396d84e260e48dae733ef = L.circleMarker(
                [26.210231221719454, 50.19240497737557],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_173e2c92c5a1e15dd9e05fafdf8ead2c = L.popup({
  "maxWidth": 200,
});

        
            
                var html_266171615521ce26fdcba03fb3fb5fdb = $(`<div id="html_266171615521ce26fdcba03fb3fb5fdb" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY279<br>             <b>Area:</b> حي الجسر<br>             <b>Orders:</b> 221<br>             <b>Distance:</b> 380.3 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_173e2c92c5a1e15dd9e05fafdf8ead2c.setContent(html_266171615521ce26fdcba03fb3fb5fdb);
            
        

        circle_marker_09937309e6c396d84e260e48dae733ef.bindPopup(popup_173e2c92c5a1e15dd9e05fafdf8ead2c)
        ;

        
    
    
            var circle_marker_1931b07945bc23ce57a8c37db93bc5e9 = L.circleMarker(
                [24.61918633093525, 46.5091345323741],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_160c96510d866407ebbd8924e3979e08 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_73c7df5e1953ce47c13e4fb527af71b4 = $(`<div id="html_73c7df5e1953ce47c13e4fb527af71b4" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY28<br>             <b>Area:</b> Dhahrat Laban<br>             <b>Orders:</b> 417<br>             <b>Distance:</b> 30.4 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_160c96510d866407ebbd8924e3979e08.setContent(html_73c7df5e1953ce47c13e4fb527af71b4);
            
        

        circle_marker_1931b07945bc23ce57a8c37db93bc5e9.bindPopup(popup_160c96510d866407ebbd8924e3979e08)
        ;

        
    
    
            var circle_marker_3b3b47d950f500b7d5d1cf02edb051b3 = L.circleMarker(
                [26.343214285714286, 50.09624523809524],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_3d10e7117540946f20a8d891b691aa4b = L.popup({
  "maxWidth": 200,
});

        
            
                var html_62cd2ebb65ebcd30658d06c62a923e14 = $(`<div id="html_62cd2ebb65ebcd30658d06c62a923e14" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY281<br>             <b>Area:</b> Ash Shulah<br>             <b>Orders:</b> 42<br>             <b>Distance:</b> 378.5 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_3d10e7117540946f20a8d891b691aa4b.setContent(html_62cd2ebb65ebcd30658d06c62a923e14);
            
        

        circle_marker_3b3b47d950f500b7d5d1cf02edb051b3.bindPopup(popup_3d10e7117540946f20a8d891b691aa4b)
        ;

        
    
    
            var circle_marker_3b3671a71200597f1076aebbbd8f9bac = L.circleMarker(
                [26.322973684210528, 50.00054736842105],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_c24cf62a6b89559c3f7488da154f393b = L.popup({
  "maxWidth": 200,
});

        
            
                var html_1620f3b58481fad04a21798c3cfefca2 = $(`<div id="html_1620f3b58481fad04a21798c3cfefca2" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY283<br>             <b>Area:</b> حي الإسكان الجنوبي<br>             <b>Orders:</b> 57<br>             <b>Distance:</b> 369.0 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_c24cf62a6b89559c3f7488da154f393b.setContent(html_1620f3b58481fad04a21798c3cfefca2);
            
        

        circle_marker_3b3671a71200597f1076aebbbd8f9bac.bindPopup(popup_c24cf62a6b89559c3f7488da154f393b)
        ;

        
    
    
            var circle_marker_574040723298b486f78e55bb8e843833 = L.circleMarker(
                [26.2538, 50.0667],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_1bdd620e1dde7f4af48192130817241c = L.popup({
  "maxWidth": 200,
});

        
            
                var html_fec0fa94008d006583bb92c6b905cb50 = $(`<div id="html_fec0fa94008d006583bb92c6b905cb50" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY285<br>             <b>Area:</b> أجيال<br>             <b>Orders:</b> 2<br>             <b>Distance:</b> 371.3 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_1bdd620e1dde7f4af48192130817241c.setContent(html_fec0fa94008d006583bb92c6b905cb50);
            
        

        circle_marker_574040723298b486f78e55bb8e843833.bindPopup(popup_1bdd620e1dde7f4af48192130817241c)
        ;

        
    
    
            var circle_marker_83858557d8862a7482b59136dce7ab8f = L.circleMarker(
                [26.233641666666667, 50.09274166666666],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_c3103499048640f9ab1799eb1d2dc4e0 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_e1b97816ce736fefd89a94003a9ad953 = $(`<div id="html_e1b97816ce736fefd89a94003a9ad953" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY288<br>             <b>Area:</b> King Abdel Aziz Air Base<br>             <b>Orders:</b> 12<br>             <b>Distance:</b> 372.6 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_c3103499048640f9ab1799eb1d2dc4e0.setContent(html_e1b97816ce736fefd89a94003a9ad953);
            
        

        circle_marker_83858557d8862a7482b59136dce7ab8f.bindPopup(popup_c3103499048640f9ab1799eb1d2dc4e0)
        ;

        
    
    
            var circle_marker_4dd0155253925df20289858c01b3f4fc = L.circleMarker(
                [26.24497142857143, 50.0364],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_1c542ede5cf6d136b23be8d65824884e = L.popup({
  "maxWidth": 200,
});

        
            
                var html_efe57860200ad73adc4cf54f10a2c098 = $(`<div id="html_efe57860200ad73adc4cf54f10a2c098" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY290<br>             <b>Area:</b> nan<br>             <b>Orders:</b> 14<br>             <b>Distance:</b> 368.1 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_1c542ede5cf6d136b23be8d65824884e.setContent(html_efe57860200ad73adc4cf54f10a2c098);
            
        

        circle_marker_4dd0155253925df20289858c01b3f4fc.bindPopup(popup_1c542ede5cf6d136b23be8d65824884e)
        ;

        
    
    
            var circle_marker_353e6d0702f6ef47062301c4ea72ad6c = L.circleMarker(
                [26.341968098159505, 50.16246687116565],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_3d2abfacb1163962ac3fe210159825f5 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_1de1f0a30cf0adbd6915eb8a22cacce5 = $(`<div id="html_1de1f0a30cf0adbd6915eb8a22cacce5" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY291<br>             <b>Area:</b> حي الدوحة الجنوبية<br>             <b>Orders:</b> 163<br>             <b>Distance:</b> 384.3 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_3d2abfacb1163962ac3fe210159825f5.setContent(html_1de1f0a30cf0adbd6915eb8a22cacce5);
            
        

        circle_marker_353e6d0702f6ef47062301c4ea72ad6c.bindPopup(popup_3d2abfacb1163962ac3fe210159825f5)
        ;

        
    
    
            var circle_marker_27f08d171cad5b508fb81fe25750e0fc = L.circleMarker(
                [24.901911083743844, 46.616907142857144],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_2604345646d4173fd17df5275f306db0 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_e485024c04759578ff7d7097fdcc0f7a = $(`<div id="html_e485024c04759578ff7d7097fdcc0f7a" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY3<br>             <b>Area:</b> حي العارض<br>             <b>Orders:</b> 406<br>             <b>Distance:</b> 30.2 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_2604345646d4173fd17df5275f306db0.setContent(html_e485024c04759578ff7d7097fdcc0f7a);
            
        

        circle_marker_27f08d171cad5b508fb81fe25750e0fc.bindPopup(popup_2604345646d4173fd17df5275f306db0)
        ;

        
    
    
            var circle_marker_cfe917f7dc944bef68a52f5c99b61749 = L.circleMarker(
                [24.60328396039604, 46.631145742574255],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_09951fe57a1e00954ffdde81387225f5 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_957e0cf3d56dcf35b229806d62603a45 = $(`<div id="html_957e0cf3d56dcf35b229806d62603a45" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY38<br>             <b>Area:</b> حي العريجاء الوسطى<br>             <b>Orders:</b> 505<br>             <b>Distance:</b> 19.5 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_09951fe57a1e00954ffdde81387225f5.setContent(html_957e0cf3d56dcf35b229806d62603a45);
            
        

        circle_marker_cfe917f7dc944bef68a52f5c99b61749.bindPopup(popup_09951fe57a1e00954ffdde81387225f5)
        ;

        
    
    
            var circle_marker_551a6474bc788a9e72fe7e9fc4e9fe23 = L.circleMarker(
                [24.594085853658534, 46.67416756097561],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_6e5d76484cec7997dae3101d1761c604 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_e43c320f3f225297c08aee119f6fbb96 = $(`<div id="html_e43c320f3f225297c08aee119f6fbb96" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY39<br>             <b>Area:</b> As Suwaidi<br>             <b>Orders:</b> 410<br>             <b>Distance:</b> 16.5 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_6e5d76484cec7997dae3101d1761c604.setContent(html_e43c320f3f225297c08aee119f6fbb96);
            
        

        circle_marker_551a6474bc788a9e72fe7e9fc4e9fe23.bindPopup(popup_6e5d76484cec7997dae3101d1761c604)
        ;

        
    
    
            var circle_marker_fe6daa6d3caf2d1fe39029a0e2efc637 = L.circleMarker(
                [24.83406755319149, 46.7888340425532],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_5a3c8202a05eca3b8e777ec2d9dd1f28 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_043d4553b908ba3afb16693fb4b2d993 = $(`<div id="html_043d4553b908ba3afb16693fb4b2d993" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY4<br>             <b>Area:</b> حي المونسية<br>             <b>Orders:</b> 188<br>             <b>Distance:</b> 16.3 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_5a3c8202a05eca3b8e777ec2d9dd1f28.setContent(html_043d4553b908ba3afb16693fb4b2d993);
            
        

        circle_marker_fe6daa6d3caf2d1fe39029a0e2efc637.bindPopup(popup_5a3c8202a05eca3b8e777ec2d9dd1f28)
        ;

        
    
    
            var circle_marker_a64980af752935b5a33cd2606918deaf = L.circleMarker(
                [24.58717641509434, 46.64893113207547],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_7ddf07eeae77b71ba2f767db71c657cf = L.popup({
  "maxWidth": 200,
});

        
            
                var html_a4dcab3068437f63d3b96dadaaa2109a = $(`<div id="html_a4dcab3068437f63d3b96dadaaa2109a" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY40<br>             <b>Area:</b> حي الزهرة<br>             <b>Orders:</b> 530<br>             <b>Distance:</b> 18.9 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_7ddf07eeae77b71ba2f767db71c657cf.setContent(html_a4dcab3068437f63d3b96dadaaa2109a);
            
        

        circle_marker_a64980af752935b5a33cd2606918deaf.bindPopup(popup_7ddf07eeae77b71ba2f767db71c657cf)
        ;

        
    
    
            var circle_marker_d3c249704dbea6c8dfcbcbd7d318a789 = L.circleMarker(
                [24.562836334405144, 46.70471254019293],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_88e9bbd05c9192b8bdd5fd84314c4f6a = L.popup({
  "maxWidth": 200,
});

        
            
                var html_9eaa2c50f23289221649840eb181801e = $(`<div id="html_9eaa2c50f23289221649840eb181801e" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY45<br>             <b>Area:</b> Ash Shifa<br>             <b>Orders:</b> 311<br>             <b>Distance:</b> 16.9 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_88e9bbd05c9192b8bdd5fd84314c4f6a.setContent(html_9eaa2c50f23289221649840eb181801e);
            
        

        circle_marker_d3c249704dbea6c8dfcbcbd7d318a789.bindPopup(popup_88e9bbd05c9192b8bdd5fd84314c4f6a)
        ;

        
    
    
            var circle_marker_0bf022d9a538404e77700d8baec95163 = L.circleMarker(
                [24.537836344537816, 46.720433193277316],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_025ee07d7fb10cd0654c8d7f7be2ed23 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_6cda38e4126105c746894391b9440f5c = $(`<div id="html_6cda38e4126105c746894391b9440f5c" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY46<br>             <b>Area:</b> حي بدر<br>             <b>Orders:</b> 476<br>             <b>Distance:</b> 18.5 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_025ee07d7fb10cd0654c8d7f7be2ed23.setContent(html_6cda38e4126105c746894391b9440f5c);
            
        

        circle_marker_0bf022d9a538404e77700d8baec95163.bindPopup(popup_025ee07d7fb10cd0654c8d7f7be2ed23)
        ;

        
    
    
            var circle_marker_fed9f56002342be411e77fc78d40f884 = L.circleMarker(
                [24.53947015503876, 46.64863062015504],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_9b8890d005e6f1b8d24c26d146d583dc = L.popup({
  "maxWidth": 200,
});

        
            
                var html_da716bf5ab48cbf19a23f768badc8478 = $(`<div id="html_da716bf5ab48cbf19a23f768badc8478" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY47<br>             <b>Area:</b> حي الحزم<br>             <b>Orders:</b> 258<br>             <b>Distance:</b> 22.5 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_9b8890d005e6f1b8d24c26d146d583dc.setContent(html_da716bf5ab48cbf19a23f768badc8478);
            
        

        circle_marker_fed9f56002342be411e77fc78d40f884.bindPopup(popup_9b8890d005e6f1b8d24c26d146d583dc)
        ;

        
    
    
            var circle_marker_94d60f877d88e03b781707e64745813c = L.circleMarker(
                [24.572298156682027, 46.68854516129032],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_cee47e724ffad161834d7287376caa60 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_c83f0bb6fdd80d4da1d94d06eb598234 = $(`<div id="html_c83f0bb6fdd80d4da1d94d06eb598234" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY48<br>             <b>Area:</b> حي الشفا<br>             <b>Orders:</b> 217<br>             <b>Distance:</b> 17.1 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_cee47e724ffad161834d7287376caa60.setContent(html_c83f0bb6fdd80d4da1d94d06eb598234);
            
        

        circle_marker_94d60f877d88e03b781707e64745813c.bindPopup(popup_cee47e724ffad161834d7287376caa60)
        ;

        
    
    
            var circle_marker_11088240dd41ac51198561490fcb7417 = L.circleMarker(
                [24.82509935897436, 46.64812147435897],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_0c417d04d040ebac85a488f7f6df5ee6 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_4b8124bb7810f5227370a55a3ce05efc = $(`<div id="html_4b8124bb7810f5227370a55a3ce05efc" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY49<br>             <b>Area:</b> حي الياسمين<br>             <b>Orders:</b> 312<br>             <b>Distance:</b> 21.7 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_0c417d04d040ebac85a488f7f6df5ee6.setContent(html_4b8124bb7810f5227370a55a3ce05efc);
            
        

        circle_marker_11088240dd41ac51198561490fcb7417.bindPopup(popup_0c417d04d040ebac85a488f7f6df5ee6)
        ;

        
    
    
            var circle_marker_7c5cb95c51aa524d3f77c786b69ad059 = L.circleMarker(
                [24.817914285714284, 46.744079670329675],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_5dc8ffbd99ffc2043dffadd59d87d2df = L.popup({
  "maxWidth": 200,
});

        
            
                var html_d5248934735269e44879e90b6c4a0fc5 = $(`<div id="html_d5248934735269e44879e90b6c4a0fc5" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY5<br>             <b>Area:</b> Qurtubah<br>             <b>Orders:</b> 182<br>             <b>Distance:</b> 15.6 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_5dc8ffbd99ffc2043dffadd59d87d2df.setContent(html_d5248934735269e44879e90b6c4a0fc5);
            
        

        circle_marker_7c5cb95c51aa524d3f77c786b69ad059.bindPopup(popup_5dc8ffbd99ffc2043dffadd59d87d2df)
        ;

        
    
    
            var circle_marker_d581e93296ad1a746f7ff1c3a19882b5 = L.circleMarker(
                [24.80893384615385, 46.690186153846156],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_dcd351087a25d89330c8b894f1321d32 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_a40ee074538159ff74852bbe1bf8abb9 = $(`<div id="html_a40ee074538159ff74852bbe1bf8abb9" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY50<br>             <b>Area:</b> حي الوادي<br>             <b>Orders:</b> 195<br>             <b>Distance:</b> 17.5 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_dcd351087a25d89330c8b894f1321d32.setContent(html_a40ee074538159ff74852bbe1bf8abb9);
            
        

        circle_marker_d581e93296ad1a746f7ff1c3a19882b5.bindPopup(popup_dcd351087a25d89330c8b894f1321d32)
        ;

        
    
    
            var circle_marker_d683051c9507f3e58c4442eef9659831 = L.circleMarker(
                [24.791567816091952, 46.67296896551724],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_5074814aabf2b54668bac5e451f0ec42 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_34697a0b8428b43a5330852f3dba9790 = $(`<div id="html_34697a0b8428b43a5330852f3dba9790" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY51<br>             <b>Area:</b> An Nafal<br>             <b>Orders:</b> 174<br>             <b>Distance:</b> 17.3 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_5074814aabf2b54668bac5e451f0ec42.setContent(html_34697a0b8428b43a5330852f3dba9790);
            
        

        circle_marker_d683051c9507f3e58c4442eef9659831.bindPopup(popup_5074814aabf2b54668bac5e451f0ec42)
        ;

        
    
    
            var circle_marker_77ba783d27edb20c00b13152ddbea52f = L.circleMarker(
                [24.82930754716981, 46.57840503144654],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_a70af639deb8273c3766e3502b7b80b1 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_6f87b966d830183145ff0bba821064cd = $(`<div id="html_6f87b966d830183145ff0bba821064cd" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY52<br>             <b>Area:</b> حي القيروان<br>             <b>Orders:</b> 159<br>             <b>Distance:</b> 27.4 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_a70af639deb8273c3766e3502b7b80b1.setContent(html_6f87b966d830183145ff0bba821064cd);
            
        

        circle_marker_77ba783d27edb20c00b13152ddbea52f.bindPopup(popup_a70af639deb8273c3766e3502b7b80b1)
        ;

        
    
    
            var circle_marker_4316169df4c898d144a25db2d54deb31 = L.circleMarker(
                [24.778615, 46.58660375],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_366d99d13cd9825ecd01165c231d2b0f = L.popup({
  "maxWidth": 200,
});

        
            
                var html_66030b3c4ac2d18dd445f31f66a446dd = $(`<div id="html_66030b3c4ac2d18dd445f31f66a446dd" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY53<br>             <b>Area:</b> حي حطين<br>             <b>Orders:</b> 80<br>             <b>Distance:</b> 23.8 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_366d99d13cd9825ecd01165c231d2b0f.setContent(html_66030b3c4ac2d18dd445f31f66a446dd);
            
        

        circle_marker_4316169df4c898d144a25db2d54deb31.bindPopup(popup_366d99d13cd9825ecd01165c231d2b0f)
        ;

        
    
    
            var circle_marker_f5bd09743e393fb9092a9f99eb758ff7 = L.circleMarker(
                [24.761924074074074, 46.59938888888889],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_081114b1b3824ef72a475e74cc1d3e27 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_f3d999d2d748adcc55cc409fb1139aaf = $(`<div id="html_f3d999d2d748adcc55cc409fb1139aaf" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY54<br>             <b>Area:</b> حي حطين<br>             <b>Orders:</b> 108<br>             <b>Distance:</b> 21.9 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_081114b1b3824ef72a475e74cc1d3e27.setContent(html_f3d999d2d748adcc55cc409fb1139aaf);
            
        

        circle_marker_f5bd09743e393fb9092a9f99eb758ff7.bindPopup(popup_081114b1b3824ef72a475e74cc1d3e27)
        ;

        
    
    
            var circle_marker_b38c3079e3c72128f48668c683db199b = L.circleMarker(
                [24.587217786561265, 46.60894031620553],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_692c9f25c6ffa5a5f49efa3856ff499d = L.popup({
  "maxWidth": 200,
});

        
            
                var html_2e975e89118b37cf99abc9c32a427397 = $(`<div id="html_2e975e89118b37cf99abc9c32a427397" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY6<br>             <b>Area:</b> حي العريجاء الغربي<br>             <b>Orders:</b> 506<br>             <b>Distance:</b> 22.3 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_692c9f25c6ffa5a5f49efa3856ff499d.setContent(html_2e975e89118b37cf99abc9c32a427397);
            
        

        circle_marker_b38c3079e3c72128f48668c683db199b.bindPopup(popup_692c9f25c6ffa5a5f49efa3856ff499d)
        ;

        
    
    
            var circle_marker_f8c56ed802dd158d850260885137b2f3 = L.circleMarker(
                [24.758169318181817, 46.63801704545454],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_dbae44efc9a60c5f6df791683ddd173c = L.popup({
  "maxWidth": 200,
});

        
            
                var html_2459199b1fbf01b69d0fadc8ecbbedad = $(`<div id="html_2459199b1fbf01b69d0fadc8ecbbedad" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY69<br>             <b>Area:</b> حي العقيق<br>             <b>Orders:</b> 88<br>             <b>Distance:</b> 18.2 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_dbae44efc9a60c5f6df791683ddd173c.setContent(html_2459199b1fbf01b69d0fadc8ecbbedad);
            
        

        circle_marker_f8c56ed802dd158d850260885137b2f3.bindPopup(popup_dbae44efc9a60c5f6df791683ddd173c)
        ;

        
    
    
            var circle_marker_d445648ade4bd9f04bbe29a22a849c37 = L.circleMarker(
                [24.74096153846154, 46.59045824175824],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_e20866993afac6a2640a8b4174578200 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_96c3ef3c0b2ffc44ac767b68044d6930 = $(`<div id="html_96c3ef3c0b2ffc44ac767b68044d6930" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY70<br>             <b>Area:</b> An Nakheel<br>             <b>Orders:</b> 91<br>             <b>Distance:</b> 22.0 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_e20866993afac6a2640a8b4174578200.setContent(html_96c3ef3c0b2ffc44ac767b68044d6930);
            
        

        circle_marker_d445648ade4bd9f04bbe29a22a849c37.bindPopup(popup_e20866993afac6a2640a8b4174578200)
        ;

        
    
    
            var circle_marker_cc293409e602e1042357b9987fef00e6 = L.circleMarker(
                [24.719071590909092, 46.62680454545455],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_e03b6dfa4f8835597d14d082952b611b = L.popup({
  "maxWidth": 200,
});

        
            
                var html_efe0cfdbdf92ab5ba976d8fd3c090ecc = $(`<div id="html_efe0cfdbdf92ab5ba976d8fd3c090ecc" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY71<br>             <b>Area:</b> King Saud University<br>             <b>Orders:</b> 88<br>             <b>Distance:</b> 17.9 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_e03b6dfa4f8835597d14d082952b611b.setContent(html_efe0cfdbdf92ab5ba976d8fd3c090ecc);
            
        

        circle_marker_cc293409e602e1042357b9987fef00e6.bindPopup(popup_e03b6dfa4f8835597d14d082952b611b)
        ;

        
    
    
            var circle_marker_0df34aeda49aedbd54056312aa224e24 = L.circleMarker(
                [24.73854875, 46.623540000000006],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_79d089a798b8ba08b4ce111c3bbd8c42 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_98ac055fd9499622fd427351602a59b5 = $(`<div id="html_98ac055fd9499622fd427351602a59b5" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY72<br>             <b>Area:</b> King Saud University<br>             <b>Orders:</b> 80<br>             <b>Distance:</b> 18.7 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_79d089a798b8ba08b4ce111c3bbd8c42.setContent(html_98ac055fd9499622fd427351602a59b5);
            
        

        circle_marker_0df34aeda49aedbd54056312aa224e24.bindPopup(popup_79d089a798b8ba08b4ce111c3bbd8c42)
        ;

        
    
    
            var circle_marker_f4b811227e55c0e98f694108a84bdd92 = L.circleMarker(
                [24.723664285714285, 46.64756666666666],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_f04fe4a61552c20eb4bbd86a4b0a198c = L.popup({
  "maxWidth": 200,
});

        
            
                var html_699df4e4d6da0b75c8f05d59bfdde30c = $(`<div id="html_699df4e4d6da0b75c8f05d59bfdde30c" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY73<br>             <b>Area:</b> King Saud University<br>             <b>Orders:</b> 42<br>             <b>Distance:</b> 15.9 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_f04fe4a61552c20eb4bbd86a4b0a198c.setContent(html_699df4e4d6da0b75c8f05d59bfdde30c);
            
        

        circle_marker_f4b811227e55c0e98f694108a84bdd92.bindPopup(popup_f04fe4a61552c20eb4bbd86a4b0a198c)
        ;

        
    
    
            var circle_marker_e3f1178c43726c8dc14fc44d6f0a57e4 = L.circleMarker(
                [24.69147291666667, 46.64316145833333],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_eab167d7a46d2cff9ed3bc056b2b0658 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_55474637ade0fbbbbb4ecaaa3924d62a = $(`<div id="html_55474637ade0fbbbbb4ecaaa3924d62a" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY75<br>             <b>Area:</b> حي ام الحمام الغربي<br>             <b>Orders:</b> 96<br>             <b>Distance:</b> 15.9 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_eab167d7a46d2cff9ed3bc056b2b0658.setContent(html_55474637ade0fbbbbb4ecaaa3924d62a);
            
        

        circle_marker_e3f1178c43726c8dc14fc44d6f0a57e4.bindPopup(popup_eab167d7a46d2cff9ed3bc056b2b0658)
        ;

        
    
    
            var circle_marker_523adbd0070857d01264621d91699fc9 = L.circleMarker(
                [24.680716000000004, 46.60694900000001],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_3d1ead0f93b9274877f086e26f3c190a = L.popup({
  "maxWidth": 200,
});

        
            
                var html_b6ea7e8f1d805ba2bfeaf272a1b8c06e = $(`<div id="html_b6ea7e8f1d805ba2bfeaf272a1b8c06e" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY79<br>             <b>Area:</b> 'Irqah<br>             <b>Orders:</b> 100<br>             <b>Distance:</b> 19.6 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_3d1ead0f93b9274877f086e26f3c190a.setContent(html_b6ea7e8f1d805ba2bfeaf272a1b8c06e);
            
        

        circle_marker_523adbd0070857d01264621d91699fc9.bindPopup(popup_3d1ead0f93b9274877f086e26f3c190a)
        ;

        
    
    
            var circle_marker_b92c991d9348c269c1bac934d8f5150b = L.circleMarker(
                [24.83927152777778, 46.678721527777775],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_1f39e3a3aee5a96f03f54d994e1cac80 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_583bd3352906ee37d4d7541f6d3d2ab8 = $(`<div id="html_583bd3352906ee37d4d7541f6d3d2ab8" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY8<br>             <b>Area:</b> حي النرجس<br>             <b>Orders:</b> 144<br>             <b>Distance:</b> 20.9 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_1f39e3a3aee5a96f03f54d994e1cac80.setContent(html_583bd3352906ee37d4d7541f6d3d2ab8);
            
        

        circle_marker_b92c991d9348c269c1bac934d8f5150b.bindPopup(popup_1f39e3a3aee5a96f03f54d994e1cac80)
        ;

        
    
    
            var circle_marker_84b05d5ab2adb1a8eee09f0d1e39b58f = L.circleMarker(
                [24.811776178010472, 46.89323821989529],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_a708e1670e4f8715b900e7db0cd759ef = L.popup({
  "maxWidth": 200,
});

        
            
                var html_631858cf4d7ffaab661953e6a2a34166 = $(`<div id="html_631858cf4d7ffaab661953e6a2a34166" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY80<br>             <b>Area:</b> Al Nadwa<br>             <b>Orders:</b> 382<br>             <b>Distance:</b> 16.7 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_a708e1670e4f8715b900e7db0cd759ef.setContent(html_631858cf4d7ffaab661953e6a2a34166);
            
        

        circle_marker_84b05d5ab2adb1a8eee09f0d1e39b58f.bindPopup(popup_a708e1670e4f8715b900e7db0cd759ef)
        ;

        
    
    
            var circle_marker_29faf9f9a154ba6c79342b2bc81c720f = L.circleMarker(
                [24.800831381733023, 46.88558454332553],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_164b1bdbabbec43db67ba9b2c240910b = L.popup({
  "maxWidth": 200,
});

        
            
                var html_9f4c50adf1079707926fc69b09ae0ffb = $(`<div id="html_9f4c50adf1079707926fc69b09ae0ffb" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY81<br>             <b>Area:</b> Al Nadwa<br>             <b>Orders:</b> 427<br>             <b>Distance:</b> 15.2 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_164b1bdbabbec43db67ba9b2c240910b.setContent(html_9f4c50adf1079707926fc69b09ae0ffb);
            
        

        circle_marker_29faf9f9a154ba6c79342b2bc81c720f.bindPopup(popup_164b1bdbabbec43db67ba9b2c240910b)
        ;

        
    
    
            var circle_marker_77aaa47e2cedefe2671bdf7f1e2a6c24 = L.circleMarker(
                [24.85414682926829, 46.83232292682927],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(feature_group_70777dd09e6799d56805a52b0862801d);
        
    
        var popup_f590da287bb6c4902dc66301f298458b = L.popup({
  "maxWidth": 200,
});

        
            
                var html_02505d55dad0f62c7798af9e49d3b717 = $(`<div id="html_02505d55dad0f62c7798af9e49d3b717" style="width: 100.0%; height: 100.0%;">             <b>Branch:</b> PHARMACIATY9<br>             <b>Area:</b> حي الرمال<br>             <b>Orders:</b> 205<br>             <b>Distance:</b> 18.8 km<br>             <b>Tier:</b> Tier 3             </div>`)[0];
                popup_f590da287bb6c4902dc66301f298458b.setContent(html_02505d55dad0f62c7798af9e49d3b717);
            
        

        circle_marker_77aaa47e2cedefe2671bdf7f1e2a6c24.bindPopup(popup_f590da287bb6c4902dc66301f298458b)
        ;

        
    
    
            feature_group_70777dd09e6799d56805a52b0862801d.addTo(map_c9740a3e5c262bf5eaf15ae69f13c876);
        
    
            var feature_group_a98170c230b5fcfb3b33c50dbb781420 = L.featureGroup(
                {
}
            );
        
    
            feature_group_a98170c230b5fcfb3b33c50dbb781420.addTo(map_c9740a3e5c262bf5eaf15ae69f13c876);
        
    
            var layer_control_65d075334859a810c0b6fb30ed03e874_layers = {
                base_layers : {
                    "openstreetmap" : tile_layer_51ab93b0c0a9f321e5bc1a25223144ee,
                },
                overlays :  {
                    "Tier 1 Branches" : feature_group_a7e59382f86081ceb863282d40faf6e1,
                    "Tier 2 Branches" : feature_group_1e0b3e902fb1c3c0fe9e02abefe19459,
                    "Tier 3 Branches" : feature_group_70777dd09e6799d56805a52b0862801d,
                    "Unclassified Branches" : feature_group_a98170c230b5fcfb3b33c50dbb781420,
                },
            };
            let layer_control_65d075334859a810c0b6fb30ed03e874 = L.control.layers(
                layer_control_65d075334859a810c0b6fb30ed03e874_layers.base_layers,
                layer_control_65d075334859a810c0b6fb30ed03e874_layers.overlays,
                {
  "position": "topright",
  "collapsed": true,
  "autoZIndex": true,
}
            ).addTo(map_c9740a3e5c262bf5eaf15ae69f13c876);

        
</script>
</html>