<!DOCTYPE html>
<html>
<head>
    
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script src="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="https://netdna.bootstrapcdn.com/bootstrap/3.0.0/css/bootstrap-glyphicons.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.2.0/css/all.min.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/python-visualization/folium/folium/templates/leaflet.awesome.rotate.min.css"/>
    
            <meta name="viewport" content="width=device-width,
                initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
            <style>
                #map_ea04b933ce08162cc5c7486923a91d62 {
                    position: relative;
                    width: 100.0%;
                    height: 100.0%;
                    left: 0.0%;
                    top: 0.0%;
                }
                .leaflet-container { font-size: 1rem; }
            </style>

            <style>html, body {
                width: 100%;
                height: 100%;
                margin: 0;
                padding: 0;
            }
            </style>

            <style>#map {
                position:absolute;
                top:0;
                bottom:0;
                right:0;
                left:0;
                }
            </style>

            <script>
                L_NO_TOUCH = false;
                L_DISABLE_3D = false;
            </script>

        
</head>
<body>
    
    
            <div class="folium-map" id="map_ea04b933ce08162cc5c7486923a91d62" ></div>
        
</body>
<script>
    
    
            var map_ea04b933ce08162cc5c7486923a91d62 = L.map(
                "map_ea04b933ce08162cc5c7486923a91d62",
                {
                    center: [24.68769, 46.80032],
                    crs: L.CRS.EPSG3857,
                    ...{
  "zoom": 10,
  "zoomControl": true,
  "preferCanvas": false,
}

                }
            );

            

        
    
            var tile_layer_d38f5fbe0d0782c280a31eb78c1613e3 = L.tileLayer(
                "https://tile.openstreetmap.org/{z}/{x}/{y}.png",
                {
  "minZoom": 0,
  "maxZoom": 19,
  "maxNativeZoom": 19,
  "noWrap": false,
  "attribution": "\u0026copy; \u003ca href=\"https://www.openstreetmap.org/copyright\"\u003eOpenStreetMap\u003c/a\u003e contributors",
  "subdomains": "abc",
  "detectRetina": false,
  "tms": false,
  "opacity": 1,
}

            );
        
    
            tile_layer_d38f5fbe0d0782c280a31eb78c1613e3.addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
            var marker_8625ef588dee746eae9fb4a68ace9f10 = L.marker(
                [24.68769, 46.80032],
                {
}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
            var icon_5de976c4161befe944633c72f8a29144 = L.AwesomeMarkers.icon(
                {
  "markerColor": "black",
  "iconColor": "white",
  "icon": "star",
  "prefix": "glyphicon",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_a72af33af4257a2864bfb9e1dabe241d = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_71e8d6db69c19a139030cfa1035d9b28 = $(`<div id="html_71e8d6db69c19a139030cfa1035d9b28" style="width: 100.0%; height: 100.0%;">Central Hub / Dark Store</div>`)[0];
                popup_a72af33af4257a2864bfb9e1dabe241d.setContent(html_71e8d6db69c19a139030cfa1035d9b28);
            
        

        marker_8625ef588dee746eae9fb4a68ace9f10.bindPopup(popup_a72af33af4257a2864bfb9e1dabe241d)
        ;

        
    
    
                marker_8625ef588dee746eae9fb4a68ace9f10.setIcon(icon_5de976c4161befe944633c72f8a29144);
            
    
            var circle_marker_ff5c8d731cb56438eae5e8c72e619fb1 = L.circleMarker(
                [24.575035230352302, 46.555144850948516],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.6, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 19.340281809115208, "stroke": true, "weight": 3}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
        var popup_d00490c85630c221f6cab982f967b322 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_52309a8878d7239736e3081a78dbc917 = $(`<div id="html_52309a8878d7239736e3081a78dbc917" style="width: 100.0%; height: 100.0%;">             <b>Rank: 1</b><br>             <b>Branch:</b> PHARMACIATY10<br>             <b>Order Volume:</b> 738<br>             <b>Tier:</b> Tier 3<br>             <b>Distance:</b> 27.8 km             </div>`)[0];
                popup_d00490c85630c221f6cab982f967b322.setContent(html_52309a8878d7239736e3081a78dbc917);
            
        

        circle_marker_ff5c8d731cb56438eae5e8c72e619fb1.bindPopup(popup_d00490c85630c221f6cab982f967b322)
        ;

        
    
    
            var marker_05722a781f85663d3c1c12a4c4a63901 = L.marker(
                [24.575035230352302, 46.555144850948516],
                {
}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
            var div_icon_6ef1b2432e73318b26f87af23c0c0ad3 = L.divIcon({
  "html": "\u003cdiv style=\"font-size:10pt; color:white; font-weight:bold;\"\u003e1\u003c/div\u003e",
  "iconSize": [20, 20],
  "iconAnchor": [10, 10],
  "className": "empty",
});
        
    
                marker_05722a781f85663d3c1c12a4c4a63901.setIcon(div_icon_6ef1b2432e73318b26f87af23c0c0ad3);
            
    
            var circle_marker_e0dad2d072085f3c3c8b44658e254f46 = L.circleMarker(
                [24.635973846153846, 46.6945626923077],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.6, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 17.074866739854087, "stroke": true, "weight": 3}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
        var popup_c9784a600ae88a3deadd1751d8522510 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_3095c04879f65fe0071586a46b666318 = $(`<div id="html_3095c04879f65fe0071586a46b666318" style="width: 100.0%; height: 100.0%;">             <b>Rank: 29</b><br>             <b>Branch:</b> PHARMACIATY102<br>             <b>Order Volume:</b> 260<br>             <b>Tier:</b> Tier 2<br>             <b>Distance:</b> 12.1 km             </div>`)[0];
                popup_c9784a600ae88a3deadd1751d8522510.setContent(html_3095c04879f65fe0071586a46b666318);
            
        

        circle_marker_e0dad2d072085f3c3c8b44658e254f46.bindPopup(popup_c9784a600ae88a3deadd1751d8522510)
        ;

        
    
    
            var marker_d235f8332c179bd72299eb3902256d18 = L.marker(
                [24.635973846153846, 46.6945626923077],
                {
}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
            var div_icon_9b6320f1ea9128dbc76dfb5fbe5e549c = L.divIcon({
  "html": "\u003cdiv style=\"font-size:10pt; color:white; font-weight:bold;\"\u003e29\u003c/div\u003e",
  "iconSize": [20, 20],
  "iconAnchor": [10, 10],
  "className": "empty",
});
        
    
                marker_d235f8332c179bd72299eb3902256d18.setIcon(div_icon_9b6320f1ea9128dbc76dfb5fbe5e549c);
            
    
            var circle_marker_d7c6c38a5c8dbd992ba697a972a255dd = L.circleMarker(
                [24.575051319648097, 46.744930498533726],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.6, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 17.663771894962487, "stroke": true, "weight": 3}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
        var popup_7d10a0319c8577f5c659f51ca24a9bf2 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_9f5d712e3a04927c0b41506c9472905e = $(`<div id="html_9f5d712e3a04927c0b41506c9472905e" style="width: 100.0%; height: 100.0%;">             <b>Rank: 20</b><br>             <b>Branch:</b> PHARMACIATY106<br>             <b>Order Volume:</b> 341<br>             <b>Tier:</b> Tier 2<br>             <b>Distance:</b> 13.7 km             </div>`)[0];
                popup_7d10a0319c8577f5c659f51ca24a9bf2.setContent(html_9f5d712e3a04927c0b41506c9472905e);
            
        

        circle_marker_d7c6c38a5c8dbd992ba697a972a255dd.bindPopup(popup_7d10a0319c8577f5c659f51ca24a9bf2)
        ;

        
    
    
            var marker_b0381801225e4ec58387436896214f93 = L.marker(
                [24.575051319648097, 46.744930498533726],
                {
}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
            var div_icon_ff448dd6610afe5c8ff5b8b4434d8024 = L.divIcon({
  "html": "\u003cdiv style=\"font-size:10pt; color:white; font-weight:bold;\"\u003e20\u003c/div\u003e",
  "iconSize": [20, 20],
  "iconAnchor": [10, 10],
  "className": "empty",
});
        
    
                marker_b0381801225e4ec58387436896214f93.setIcon(div_icon_ff448dd6610afe5c8ff5b8b4434d8024);
            
    
            var circle_marker_1139ccca20bf005d1b18b0709f1095a3 = L.circleMarker(
                [24.54070909090909, 46.68694589800444],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.6, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 18.270882709389802, "stroke": true, "weight": 3}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
        var popup_9f73ab9e0a5bb162f3e68c277ac7f51d = L.popup({
  "maxWidth": 200,
});

        
            
                var html_eb6a99027bf9c727fb71f0a13380363b = $(`<div id="html_eb6a99027bf9c727fb71f0a13380363b" style="width: 100.0%; height: 100.0%;">             <b>Rank: 8</b><br>             <b>Branch:</b> PHARMACIATY107<br>             <b>Order Volume:</b> 451<br>             <b>Tier:</b> Tier 3<br>             <b>Distance:</b> 20.0 km             </div>`)[0];
                popup_9f73ab9e0a5bb162f3e68c277ac7f51d.setContent(html_eb6a99027bf9c727fb71f0a13380363b);
            
        

        circle_marker_1139ccca20bf005d1b18b0709f1095a3.bindPopup(popup_9f73ab9e0a5bb162f3e68c277ac7f51d)
        ;

        
    
    
            var marker_ddc8d4f154661d29e43f1fa8aa1b1393 = L.marker(
                [24.54070909090909, 46.68694589800444],
                {
}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
            var div_icon_e337f9ca92b2fd06e237f0d1abaa1224 = L.divIcon({
  "html": "\u003cdiv style=\"font-size:10pt; color:white; font-weight:bold;\"\u003e8\u003c/div\u003e",
  "iconSize": [20, 20],
  "iconAnchor": [10, 10],
  "className": "empty",
});
        
    
                marker_ddc8d4f154661d29e43f1fa8aa1b1393.setIcon(div_icon_e337f9ca92b2fd06e237f0d1abaa1224);
            
    
            var circle_marker_4129c0a7720ab98d9da02b09fdba0117 = L.circleMarker(
                [24.523469574944073, 46.65891476510067],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.6, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 18.251537615659682, "stroke": true, "weight": 3}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
        var popup_e09961db875fd934ce57ab1c9c9139f8 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_69afecd6a903c85bec9683f2b4189a10 = $(`<div id="html_69afecd6a903c85bec9683f2b4189a10" style="width: 100.0%; height: 100.0%;">             <b>Rank: 9</b><br>             <b>Branch:</b> PHARMACIATY109<br>             <b>Order Volume:</b> 447<br>             <b>Tier:</b> Tier 3<br>             <b>Distance:</b> 23.2 km             </div>`)[0];
                popup_e09961db875fd934ce57ab1c9c9139f8.setContent(html_69afecd6a903c85bec9683f2b4189a10);
            
        

        circle_marker_4129c0a7720ab98d9da02b09fdba0117.bindPopup(popup_e09961db875fd934ce57ab1c9c9139f8)
        ;

        
    
    
            var marker_d4eda2bc52a041d538cb9ef3c56543ff = L.marker(
                [24.523469574944073, 46.65891476510067],
                {
}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
            var div_icon_bb8df7b50a5e47523f12f691f0b8e2b8 = L.divIcon({
  "html": "\u003cdiv style=\"font-size:10pt; color:white; font-weight:bold;\"\u003e9\u003c/div\u003e",
  "iconSize": [20, 20],
  "iconAnchor": [10, 10],
  "className": "empty",
});
        
    
                marker_d4eda2bc52a041d538cb9ef3c56543ff.setIcon(div_icon_bb8df7b50a5e47523f12f691f0b8e2b8);
            
    
            var circle_marker_6589f563e3684a35716cac8337ce02ba = L.circleMarker(
                [24.87709375, 46.63676467391304],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.6, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 17.82923909336759, "stroke": true, "weight": 3}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
        var popup_6e8959dbd07a8e6bc93f1c858ac297f5 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_6d815e068c25ea3b54ba6579a74e2834 = $(`<div id="html_6d815e068c25ea3b54ba6579a74e2834" style="width: 100.0%; height: 100.0%;">             <b>Rank: 16</b><br>             <b>Branch:</b> PHARMACIATY12<br>             <b>Order Volume:</b> 368<br>             <b>Tier:</b> Tier 3<br>             <b>Distance:</b> 26.8 km             </div>`)[0];
                popup_6e8959dbd07a8e6bc93f1c858ac297f5.setContent(html_6d815e068c25ea3b54ba6579a74e2834);
            
        

        circle_marker_6589f563e3684a35716cac8337ce02ba.bindPopup(popup_6e8959dbd07a8e6bc93f1c858ac297f5)
        ;

        
    
    
            var marker_96c9746acd2fcdb9e61c80764c8fabb4 = L.marker(
                [24.87709375, 46.63676467391304],
                {
}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
            var div_icon_958a6dc7a3ec1b1e6ec29f3db9fda2b0 = L.divIcon({
  "html": "\u003cdiv style=\"font-size:10pt; color:white; font-weight:bold;\"\u003e16\u003c/div\u003e",
  "iconSize": [20, 20],
  "iconAnchor": [10, 10],
  "className": "empty",
});
        
    
                marker_96c9746acd2fcdb9e61c80764c8fabb4.setIcon(div_icon_958a6dc7a3ec1b1e6ec29f3db9fda2b0);
            
    
            var circle_marker_e80425fe709b707345ac9dd0551aea7e = L.circleMarker(
                [24.806236182336182, 46.7877831908832],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.6, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 17.72653558232912, "stroke": true, "weight": 3}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
        var popup_c4338516c9ba20694e05b543d682c641 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_537ea80fe5ae878387b8a0fa61f1d5df = $(`<div id="html_537ea80fe5ae878387b8a0fa61f1d5df" style="width: 100.0%; height: 100.0%;">             <b>Rank: 18</b><br>             <b>Branch:</b> PHARMACIATY17<br>             <b>Order Volume:</b> 351<br>             <b>Tier:</b> Tier 2<br>             <b>Distance:</b> 13.2 km             </div>`)[0];
                popup_c4338516c9ba20694e05b543d682c641.setContent(html_537ea80fe5ae878387b8a0fa61f1d5df);
            
        

        circle_marker_e80425fe709b707345ac9dd0551aea7e.bindPopup(popup_c4338516c9ba20694e05b543d682c641)
        ;

        
    
    
            var marker_11c62d570d9730ab36f5502d9cb7cf8d = L.marker(
                [24.806236182336182, 46.7877831908832],
                {
}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
            var div_icon_3adab2f075bc32bed4191f88ca4ee7fd = L.divIcon({
  "html": "\u003cdiv style=\"font-size:10pt; color:white; font-weight:bold;\"\u003e18\u003c/div\u003e",
  "iconSize": [20, 20],
  "iconAnchor": [10, 10],
  "className": "empty",
});
        
    
                marker_11c62d570d9730ab36f5502d9cb7cf8d.setIcon(div_icon_3adab2f075bc32bed4191f88ca4ee7fd);
            
    
            var circle_marker_232083ea16f2d874bb166bc31b782b1f = L.circleMarker(
                [26.363769888475836, 50.06148921933086],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.6, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 17.14876140001204, "stroke": true, "weight": 3}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
        var popup_dd9a290761f02838c52e316cc2b83cac = L.popup({
  "maxWidth": 200,
});

        
            
                var html_de93cb93aba6b60f13d105a0002e9627 = $(`<div id="html_de93cb93aba6b60f13d105a0002e9627" style="width: 100.0%; height: 100.0%;">             <b>Rank: 28</b><br>             <b>Branch:</b> PHARMACIATY245<br>             <b>Order Volume:</b> 269<br>             <b>Tier:</b> Tier 3<br>             <b>Distance:</b> 376.6 km             </div>`)[0];
                popup_dd9a290761f02838c52e316cc2b83cac.setContent(html_de93cb93aba6b60f13d105a0002e9627);
            
        

        circle_marker_232083ea16f2d874bb166bc31b782b1f.bindPopup(popup_dd9a290761f02838c52e316cc2b83cac)
        ;

        
    
    
            var marker_51f7b247a80ee17c24a59d254d2e39b9 = L.marker(
                [26.363769888475836, 50.06148921933086],
                {
}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
            var div_icon_c5bcc22ae574270500effca690db8ae6 = L.divIcon({
  "html": "\u003cdiv style=\"font-size:10pt; color:white; font-weight:bold;\"\u003e28\u003c/div\u003e",
  "iconSize": [20, 20],
  "iconAnchor": [10, 10],
  "className": "empty",
});
        
    
                marker_51f7b247a80ee17c24a59d254d2e39b9.setIcon(div_icon_c5bcc22ae574270500effca690db8ae6);
            
    
            var circle_marker_29067dd79461568a506ff1c88822a832 = L.circleMarker(
                [26.428943848580445, 50.088458044164035],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.6, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 17.505296311088756, "stroke": true, "weight": 3}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
        var popup_9ab88de4c957fc63359e92af40558a26 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_b7eff558bc1d4f44a9fef40dcbfe6964 = $(`<div id="html_b7eff558bc1d4f44a9fef40dcbfe6964" style="width: 100.0%; height: 100.0%;">             <b>Rank: 22</b><br>             <b>Branch:</b> PHARMACIATY250<br>             <b>Order Volume:</b> 317<br>             <b>Tier:</b> Tier 3<br>             <b>Distance:</b> 382.4 km             </div>`)[0];
                popup_9ab88de4c957fc63359e92af40558a26.setContent(html_b7eff558bc1d4f44a9fef40dcbfe6964);
            
        

        circle_marker_29067dd79461568a506ff1c88822a832.bindPopup(popup_9ab88de4c957fc63359e92af40558a26)
        ;

        
    
    
            var marker_d9132c4f84bbce45581f0da9c80c8f28 = L.marker(
                [26.428943848580445, 50.088458044164035],
                {
}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
            var div_icon_31dafeeb19c8336a4d4c3b4c01245e24 = L.divIcon({
  "html": "\u003cdiv style=\"font-size:10pt; color:white; font-weight:bold;\"\u003e22\u003c/div\u003e",
  "iconSize": [20, 20],
  "iconAnchor": [10, 10],
  "className": "empty",
});
        
    
                marker_d9132c4f84bbce45581f0da9c80c8f28.setIcon(div_icon_31dafeeb19c8336a4d4c3b4c01245e24);
            
    
            var circle_marker_b2810e57c0d46e6d9bdef877cf7574f6 = L.circleMarker(
                [26.39622105263158, 50.012040080971666],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.6, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 18.468634744618235, "stroke": true, "weight": 3}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
        var popup_a189f8f0ecf3f4d411ee9de7d9190049 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_75068f02b72921f312abd66fc3d1ed81 = $(`<div id="html_75068f02b72921f312abd66fc3d1ed81" style="width: 100.0%; height: 100.0%;">             <b>Rank: 6</b><br>             <b>Branch:</b> PHARMACIATY256<br>             <b>Order Volume:</b> 494<br>             <b>Tier:</b> Tier 3<br>             <b>Distance:</b> 374.0 km             </div>`)[0];
                popup_a189f8f0ecf3f4d411ee9de7d9190049.setContent(html_75068f02b72921f312abd66fc3d1ed81);
            
        

        circle_marker_b2810e57c0d46e6d9bdef877cf7574f6.bindPopup(popup_a189f8f0ecf3f4d411ee9de7d9190049)
        ;

        
    
    
            var marker_31e1db776e295dc01a704f36dfcfa128 = L.marker(
                [26.39622105263158, 50.012040080971666],
                {
}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
            var div_icon_baa2609d7203de0d6226d3ebb7f67c94 = L.divIcon({
  "html": "\u003cdiv style=\"font-size:10pt; color:white; font-weight:bold;\"\u003e6\u003c/div\u003e",
  "iconSize": [20, 20],
  "iconAnchor": [10, 10],
  "className": "empty",
});
        
    
                marker_31e1db776e295dc01a704f36dfcfa128.setIcon(div_icon_baa2609d7203de0d6226d3ebb7f67c94);
            
    
            var circle_marker_a6ac217640e8ef928e31b6d4acf05558 = L.circleMarker(
                [24.641883056478406, 46.56257707641196],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.6, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 17.39283247796922, "stroke": true, "weight": 3}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
        var popup_ce0ee3a0afabb4abda5e7233d12c1446 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_c2a7dbff55fb4b66cd78a9b2c5f3b04c = $(`<div id="html_c2a7dbff55fb4b66cd78a9b2c5f3b04c" style="width: 100.0%; height: 100.0%;">             <b>Rank: 25</b><br>             <b>Branch:</b> PHARMACIATY27<br>             <b>Order Volume:</b> 301<br>             <b>Tier:</b> Tier 3<br>             <b>Distance:</b> 24.6 km             </div>`)[0];
                popup_ce0ee3a0afabb4abda5e7233d12c1446.setContent(html_c2a7dbff55fb4b66cd78a9b2c5f3b04c);
            
        

        circle_marker_a6ac217640e8ef928e31b6d4acf05558.bindPopup(popup_ce0ee3a0afabb4abda5e7233d12c1446)
        ;

        
    
    
            var marker_1e5a4da916188ec589ff9728cdabdd5b = L.marker(
                [24.641883056478406, 46.56257707641196],
                {
}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
            var div_icon_3065cb1645897cb7a9b3463bdb94943b = L.divIcon({
  "html": "\u003cdiv style=\"font-size:10pt; color:white; font-weight:bold;\"\u003e25\u003c/div\u003e",
  "iconSize": [20, 20],
  "iconAnchor": [10, 10],
  "className": "empty",
});
        
    
                marker_1e5a4da916188ec589ff9728cdabdd5b.setIcon(div_icon_3065cb1645897cb7a9b3463bdb94943b);
            
    
            var circle_marker_f1b3c14ab830566130593cac2f47f4c9 = L.circleMarker(
                [24.61918633093525, 46.5091345323741],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.6, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 18.100680274868786, "stroke": true, "weight": 3}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
        var popup_49700dbf64b157bef1c82135f0cf423d = L.popup({
  "maxWidth": 200,
});

        
            
                var html_b93329d3d357cdd5cc379ad9c2330fc7 = $(`<div id="html_b93329d3d357cdd5cc379ad9c2330fc7" style="width: 100.0%; height: 100.0%;">             <b>Rank: 11</b><br>             <b>Branch:</b> PHARMACIATY28<br>             <b>Order Volume:</b> 417<br>             <b>Tier:</b> Tier 3<br>             <b>Distance:</b> 30.4 km             </div>`)[0];
                popup_49700dbf64b157bef1c82135f0cf423d.setContent(html_b93329d3d357cdd5cc379ad9c2330fc7);
            
        

        circle_marker_f1b3c14ab830566130593cac2f47f4c9.bindPopup(popup_49700dbf64b157bef1c82135f0cf423d)
        ;

        
    
    
            var marker_cd6d75fbc9bed3c6095eb6acc3b7e342 = L.marker(
                [24.61918633093525, 46.5091345323741],
                {
}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
            var div_icon_9f7d6efd41d958a7d5ecba06ca84c215 = L.divIcon({
  "html": "\u003cdiv style=\"font-size:10pt; color:white; font-weight:bold;\"\u003e11\u003c/div\u003e",
  "iconSize": [20, 20],
  "iconAnchor": [10, 10],
  "className": "empty",
});
        
    
                marker_cd6d75fbc9bed3c6095eb6acc3b7e342.setIcon(div_icon_9f7d6efd41d958a7d5ecba06ca84c215);
            
    
            var circle_marker_b2017abf779bb1c70c2562cf413fb8ae = L.circleMarker(
                [24.737047826086954, 46.83752666666667],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.6, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 17.689095475366372, "stroke": true, "weight": 3}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
        var popup_72daead32ff47209587f0ca863af5c9e = L.popup({
  "maxWidth": 200,
});

        
            
                var html_a26fe37d060eb590025518d0dc82dfce = $(`<div id="html_a26fe37d060eb590025518d0dc82dfce" style="width: 100.0%; height: 100.0%;">             <b>Rank: 19</b><br>             <b>Branch:</b> PHARMACIATY29<br>             <b>Order Volume:</b> 345<br>             <b>Tier:</b> Tier 2<br>             <b>Distance:</b> 6.7 km             </div>`)[0];
                popup_72daead32ff47209587f0ca863af5c9e.setContent(html_a26fe37d060eb590025518d0dc82dfce);
            
        

        circle_marker_b2017abf779bb1c70c2562cf413fb8ae.bindPopup(popup_72daead32ff47209587f0ca863af5c9e)
        ;

        
    
    
            var marker_7a25d51604ed0c9e4f94e989420bdfb3 = L.marker(
                [24.737047826086954, 46.83752666666667],
                {
}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
            var div_icon_5939f6a357b35af913f0034b95881694 = L.divIcon({
  "html": "\u003cdiv style=\"font-size:10pt; color:white; font-weight:bold;\"\u003e19\u003c/div\u003e",
  "iconSize": [20, 20],
  "iconAnchor": [10, 10],
  "className": "empty",
});
        
    
                marker_7a25d51604ed0c9e4f94e989420bdfb3.setIcon(div_icon_5939f6a357b35af913f0034b95881694);
            
    
            var circle_marker_28e0338bf2f1d5c9032be65c72d62ae4 = L.circleMarker(
                [24.901911083743844, 46.616907142857144],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.6, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 18.04263016788597, "stroke": true, "weight": 3}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
        var popup_cb9e762cd123df2ef5e9a107eb9f82d0 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_531aa37f24e62b7a0c1a96070e957101 = $(`<div id="html_531aa37f24e62b7a0c1a96070e957101" style="width: 100.0%; height: 100.0%;">             <b>Rank: 13</b><br>             <b>Branch:</b> PHARMACIATY3<br>             <b>Order Volume:</b> 406<br>             <b>Tier:</b> Tier 3<br>             <b>Distance:</b> 30.2 km             </div>`)[0];
                popup_cb9e762cd123df2ef5e9a107eb9f82d0.setContent(html_531aa37f24e62b7a0c1a96070e957101);
            
        

        circle_marker_28e0338bf2f1d5c9032be65c72d62ae4.bindPopup(popup_cb9e762cd123df2ef5e9a107eb9f82d0)
        ;

        
    
    
            var marker_556b5b89b270ead0ee1c761d0ec48eec = L.marker(
                [24.901911083743844, 46.616907142857144],
                {
}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
            var div_icon_3242b10fea862730369a63d54d2e905a = L.divIcon({
  "html": "\u003cdiv style=\"font-size:10pt; color:white; font-weight:bold;\"\u003e13\u003c/div\u003e",
  "iconSize": [20, 20],
  "iconAnchor": [10, 10],
  "className": "empty",
});
        
    
                marker_556b5b89b270ead0ee1c761d0ec48eec.setIcon(div_icon_3242b10fea862730369a63d54d2e905a);
            
    
            var circle_marker_fce6d87af19d2cd74ac33e9347e87cf2 = L.circleMarker(
                [24.61582, 46.67899517241379],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.6, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 17.31198998949478, "stroke": true, "weight": 3}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
        var popup_01d8462a7386618478dddce3c26e2b6d = L.popup({
  "maxWidth": 200,
});

        
            
                var html_f85aaba2b3273525ed142a85ef5bc76f = $(`<div id="html_f85aaba2b3273525ed142a85ef5bc76f" style="width: 100.0%; height: 100.0%;">             <b>Rank: 27</b><br>             <b>Branch:</b> PHARMACIATY37<br>             <b>Order Volume:</b> 290<br>             <b>Tier:</b> Tier 2<br>             <b>Distance:</b> 14.6 km             </div>`)[0];
                popup_01d8462a7386618478dddce3c26e2b6d.setContent(html_f85aaba2b3273525ed142a85ef5bc76f);
            
        

        circle_marker_fce6d87af19d2cd74ac33e9347e87cf2.bindPopup(popup_01d8462a7386618478dddce3c26e2b6d)
        ;

        
    
    
            var marker_f33af927e50903ad6b7766a325be584e = L.marker(
                [24.61582, 46.67899517241379],
                {
}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
            var div_icon_b89df7a5399dff76190bad2559fb3549 = L.divIcon({
  "html": "\u003cdiv style=\"font-size:10pt; color:white; font-weight:bold;\"\u003e27\u003c/div\u003e",
  "iconSize": [20, 20],
  "iconAnchor": [10, 10],
  "className": "empty",
});
        
    
                marker_f33af927e50903ad6b7766a325be584e.setIcon(div_icon_b89df7a5399dff76190bad2559fb3549);
            
    
            var circle_marker_e653b881170b3e39ae2cddfe83718248 = L.circleMarker(
                [24.60328396039604, 46.631145742574255],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.6, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 18.516456890593307, "stroke": true, "weight": 3}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
        var popup_a8b0eb8c8d164bbf845a47f063f595f9 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_4311cc4b27e5d81231c6affb0573fcb1 = $(`<div id="html_4311cc4b27e5d81231c6affb0573fcb1" style="width: 100.0%; height: 100.0%;">             <b>Rank: 5</b><br>             <b>Branch:</b> PHARMACIATY38<br>             <b>Order Volume:</b> 505<br>             <b>Tier:</b> Tier 3<br>             <b>Distance:</b> 19.5 km             </div>`)[0];
                popup_a8b0eb8c8d164bbf845a47f063f595f9.setContent(html_4311cc4b27e5d81231c6affb0573fcb1);
            
        

        circle_marker_e653b881170b3e39ae2cddfe83718248.bindPopup(popup_a8b0eb8c8d164bbf845a47f063f595f9)
        ;

        
    
    
            var marker_f2fd7121ed58838b1ea91c4d4dea9898 = L.marker(
                [24.60328396039604, 46.631145742574255],
                {
}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
            var div_icon_03738c092ff3eaf1d4b9af334b76d1b4 = L.divIcon({
  "html": "\u003cdiv style=\"font-size:10pt; color:white; font-weight:bold;\"\u003e5\u003c/div\u003e",
  "iconSize": [20, 20],
  "iconAnchor": [10, 10],
  "className": "empty",
});
        
    
                marker_f2fd7121ed58838b1ea91c4d4dea9898.setIcon(div_icon_03738c092ff3eaf1d4b9af334b76d1b4);
            
    
            var circle_marker_82e4f65f4cbfd19ffe0d3a334e7fc52d = L.circleMarker(
                [24.594085853658534, 46.67416756097561],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.6, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 18.06391928359868, "stroke": true, "weight": 3}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
        var popup_10b316c280b7e985a47a2d23f1721545 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_2f71f2a731f3c2ca7b212b349a56514c = $(`<div id="html_2f71f2a731f3c2ca7b212b349a56514c" style="width: 100.0%; height: 100.0%;">             <b>Rank: 12</b><br>             <b>Branch:</b> PHARMACIATY39<br>             <b>Order Volume:</b> 410<br>             <b>Tier:</b> Tier 3<br>             <b>Distance:</b> 16.5 km             </div>`)[0];
                popup_10b316c280b7e985a47a2d23f1721545.setContent(html_2f71f2a731f3c2ca7b212b349a56514c);
            
        

        circle_marker_82e4f65f4cbfd19ffe0d3a334e7fc52d.bindPopup(popup_10b316c280b7e985a47a2d23f1721545)
        ;

        
    
    
            var marker_d859dc384063bff804220023e75bb6ab = L.marker(
                [24.594085853658534, 46.67416756097561],
                {
}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
            var div_icon_62e8c4423ca6b0f2de5f1c5319f16e6b = L.divIcon({
  "html": "\u003cdiv style=\"font-size:10pt; color:white; font-weight:bold;\"\u003e12\u003c/div\u003e",
  "iconSize": [20, 20],
  "iconAnchor": [10, 10],
  "className": "empty",
});
        
    
                marker_d859dc384063bff804220023e75bb6ab.setIcon(div_icon_62e8c4423ca6b0f2de5f1c5319f16e6b);
            
    
            var circle_marker_88957a83361fea5148d36f8f23917235 = L.circleMarker(
                [24.58717641509434, 46.64893113207547],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.6, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 18.621379348003945, "stroke": true, "weight": 3}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
        var popup_a939793b0c65d76bf76375591b3bb41f = L.popup({
  "maxWidth": 200,
});

        
            
                var html_a51b213d7fe190516619803f05991a86 = $(`<div id="html_a51b213d7fe190516619803f05991a86" style="width: 100.0%; height: 100.0%;">             <b>Rank: 3</b><br>             <b>Branch:</b> PHARMACIATY40<br>             <b>Order Volume:</b> 530<br>             <b>Tier:</b> Tier 3<br>             <b>Distance:</b> 18.9 km             </div>`)[0];
                popup_a939793b0c65d76bf76375591b3bb41f.setContent(html_a51b213d7fe190516619803f05991a86);
            
        

        circle_marker_88957a83361fea5148d36f8f23917235.bindPopup(popup_a939793b0c65d76bf76375591b3bb41f)
        ;

        
    
    
            var marker_e56244d00e9efb8231169e577fcd52af = L.marker(
                [24.58717641509434, 46.64893113207547],
                {
}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
            var div_icon_dc5700b907289c66db4a427f4988920a = L.divIcon({
  "html": "\u003cdiv style=\"font-size:10pt; color:white; font-weight:bold;\"\u003e3\u003c/div\u003e",
  "iconSize": [20, 20],
  "iconAnchor": [10, 10],
  "className": "empty",
});
        
    
                marker_e56244d00e9efb8231169e577fcd52af.setIcon(div_icon_dc5700b907289c66db4a427f4988920a);
            
    
            var circle_marker_99c834ed412b6c7effa5ef6e14710f3b = L.circleMarker(
                [24.57182545126354, 46.779927256317684],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.6, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 18.717548823642147, "stroke": true, "weight": 3}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
        var popup_5c6ee914b67c092b5253a850e86688ee = L.popup({
  "maxWidth": 200,
});

        
            
                var html_d76a9866e17ecfe9f78709021aa9ac61 = $(`<div id="html_d76a9866e17ecfe9f78709021aa9ac61" style="width: 100.0%; height: 100.0%;">             <b>Rank: 2</b><br>             <b>Branch:</b> PHARMACIATY43<br>             <b>Order Volume:</b> 554<br>             <b>Tier:</b> Tier 2<br>             <b>Distance:</b> 13.0 km             </div>`)[0];
                popup_5c6ee914b67c092b5253a850e86688ee.setContent(html_d76a9866e17ecfe9f78709021aa9ac61);
            
        

        circle_marker_99c834ed412b6c7effa5ef6e14710f3b.bindPopup(popup_5c6ee914b67c092b5253a850e86688ee)
        ;

        
    
    
            var marker_803f73e797b70faeab92f7f160814d66 = L.marker(
                [24.57182545126354, 46.779927256317684],
                {
}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
            var div_icon_ecb7d5f24991e8e01e4a5a6d54c990b8 = L.divIcon({
  "html": "\u003cdiv style=\"font-size:10pt; color:white; font-weight:bold;\"\u003e2\u003c/div\u003e",
  "iconSize": [20, 20],
  "iconAnchor": [10, 10],
  "className": "empty",
});
        
    
                marker_803f73e797b70faeab92f7f160814d66.setIcon(div_icon_ecb7d5f24991e8e01e4a5a6d54c990b8);
            
    
            var circle_marker_4a2637b15753d1bc469e0b8978c2aa2b = L.circleMarker(
                [24.555278140703518, 46.803108542713574],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.6, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 17.999415360368438, "stroke": true, "weight": 3}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
        var popup_b93e3da3db30c40ce9e22d1eb6effec3 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_f1244b78c06debbae3c7884dbb9d28d6 = $(`<div id="html_f1244b78c06debbae3c7884dbb9d28d6" style="width: 100.0%; height: 100.0%;">             <b>Rank: 14</b><br>             <b>Branch:</b> PHARMACIATY44<br>             <b>Order Volume:</b> 398<br>             <b>Tier:</b> Tier 2<br>             <b>Distance:</b> 14.7 km             </div>`)[0];
                popup_b93e3da3db30c40ce9e22d1eb6effec3.setContent(html_f1244b78c06debbae3c7884dbb9d28d6);
            
        

        circle_marker_4a2637b15753d1bc469e0b8978c2aa2b.bindPopup(popup_b93e3da3db30c40ce9e22d1eb6effec3)
        ;

        
    
    
            var marker_e73bab211535c97d1151a009fb57a281 = L.marker(
                [24.555278140703518, 46.803108542713574],
                {
}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
            var div_icon_2b584206c2d103a5fdae09e8347f1ac7 = L.divIcon({
  "html": "\u003cdiv style=\"font-size:10pt; color:white; font-weight:bold;\"\u003e14\u003c/div\u003e",
  "iconSize": [20, 20],
  "iconAnchor": [10, 10],
  "className": "empty",
});
        
    
                marker_e73bab211535c97d1151a009fb57a281.setIcon(div_icon_2b584206c2d103a5fdae09e8347f1ac7);
            
    
            var circle_marker_96a731c44c89e320385f2797341fe80b = L.circleMarker(
                [24.562836334405144, 46.70471254019293],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.6, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 17.463801945134186, "stroke": true, "weight": 3}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
        var popup_ace938028e816113f7531f0cc36f7e55 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_cbe586eeed58a49efd54563ad5d1cb03 = $(`<div id="html_cbe586eeed58a49efd54563ad5d1cb03" style="width: 100.0%; height: 100.0%;">             <b>Rank: 24</b><br>             <b>Branch:</b> PHARMACIATY45<br>             <b>Order Volume:</b> 311<br>             <b>Tier:</b> Tier 3<br>             <b>Distance:</b> 16.9 km             </div>`)[0];
                popup_ace938028e816113f7531f0cc36f7e55.setContent(html_cbe586eeed58a49efd54563ad5d1cb03);
            
        

        circle_marker_96a731c44c89e320385f2797341fe80b.bindPopup(popup_ace938028e816113f7531f0cc36f7e55)
        ;

        
    
    
            var marker_fa3a52a63056a106b8561ed3d320ea8b = L.marker(
                [24.562836334405144, 46.70471254019293],
                {
}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
            var div_icon_33c4230f6943f337a125ae64316bfd18 = L.divIcon({
  "html": "\u003cdiv style=\"font-size:10pt; color:white; font-weight:bold;\"\u003e24\u003c/div\u003e",
  "iconSize": [20, 20],
  "iconAnchor": [10, 10],
  "className": "empty",
});
        
    
                marker_fa3a52a63056a106b8561ed3d320ea8b.setIcon(div_icon_33c4230f6943f337a125ae64316bfd18);
            
    
            var circle_marker_5ceda7de21159240020e3325a0443a09 = L.circleMarker(
                [24.537836344537816, 46.720433193277316],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.6, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 18.388034763602466, "stroke": true, "weight": 3}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
        var popup_f9560c56eb70b8c8055dfb656138cc0d = L.popup({
  "maxWidth": 200,
});

        
            
                var html_f63ce6654d1ab1bc46302439c8c3f2ae = $(`<div id="html_f63ce6654d1ab1bc46302439c8c3f2ae" style="width: 100.0%; height: 100.0%;">             <b>Rank: 7</b><br>             <b>Branch:</b> PHARMACIATY46<br>             <b>Order Volume:</b> 476<br>             <b>Tier:</b> Tier 3<br>             <b>Distance:</b> 18.5 km             </div>`)[0];
                popup_f9560c56eb70b8c8055dfb656138cc0d.setContent(html_f63ce6654d1ab1bc46302439c8c3f2ae);
            
        

        circle_marker_5ceda7de21159240020e3325a0443a09.bindPopup(popup_f9560c56eb70b8c8055dfb656138cc0d)
        ;

        
    
    
            var marker_ac5e81857e52f50d0839f77ebc36cb59 = L.marker(
                [24.537836344537816, 46.720433193277316],
                {
}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
            var div_icon_8a7d0475c8c8d11d5ff2b2ea390cd1ef = L.divIcon({
  "html": "\u003cdiv style=\"font-size:10pt; color:white; font-weight:bold;\"\u003e7\u003c/div\u003e",
  "iconSize": [20, 20],
  "iconAnchor": [10, 10],
  "className": "empty",
});
        
    
                marker_ac5e81857e52f50d0839f77ebc36cb59.setIcon(div_icon_8a7d0475c8c8d11d5ff2b2ea390cd1ef);
            
    
            var circle_marker_bfa14da2badc4095e128926832d2d050 = L.circleMarker(
                [24.53947015503876, 46.64863062015504],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.6, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 17.05809852981615, "stroke": true, "weight": 3}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
        var popup_3fe6f14b9ad5152c22c15786cbcbeba5 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_ede4056f66fefdb496f6993466b3f961 = $(`<div id="html_ede4056f66fefdb496f6993466b3f961" style="width: 100.0%; height: 100.0%;">             <b>Rank: 30</b><br>             <b>Branch:</b> PHARMACIATY47<br>             <b>Order Volume:</b> 258<br>             <b>Tier:</b> Tier 3<br>             <b>Distance:</b> 22.5 km             </div>`)[0];
                popup_3fe6f14b9ad5152c22c15786cbcbeba5.setContent(html_ede4056f66fefdb496f6993466b3f961);
            
        

        circle_marker_bfa14da2badc4095e128926832d2d050.bindPopup(popup_3fe6f14b9ad5152c22c15786cbcbeba5)
        ;

        
    
    
            var marker_abd0d454625e3fc0e26d45bf4e331dc3 = L.marker(
                [24.53947015503876, 46.64863062015504],
                {
}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
            var div_icon_d4a926b0cfcacf9d2d044698ca16a079 = L.divIcon({
  "html": "\u003cdiv style=\"font-size:10pt; color:white; font-weight:bold;\"\u003e30\u003c/div\u003e",
  "iconSize": [20, 20],
  "iconAnchor": [10, 10],
  "className": "empty",
});
        
    
                marker_abd0d454625e3fc0e26d45bf4e331dc3.setIcon(div_icon_d4a926b0cfcacf9d2d044698ca16a079);
            
    
            var circle_marker_5e2cb64d506db32e84264d0ecad9ab51 = L.circleMarker(
                [24.82509935897436, 46.64812147435897],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.6, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 17.470772970092213, "stroke": true, "weight": 3}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
        var popup_1e621dbcfd57d8a16e8cd5d38a836a31 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_c8c3cb25772de177185bc86df46653da = $(`<div id="html_c8c3cb25772de177185bc86df46653da" style="width: 100.0%; height: 100.0%;">             <b>Rank: 23</b><br>             <b>Branch:</b> PHARMACIATY49<br>             <b>Order Volume:</b> 312<br>             <b>Tier:</b> Tier 3<br>             <b>Distance:</b> 21.7 km             </div>`)[0];
                popup_1e621dbcfd57d8a16e8cd5d38a836a31.setContent(html_c8c3cb25772de177185bc86df46653da);
            
        

        circle_marker_5e2cb64d506db32e84264d0ecad9ab51.bindPopup(popup_1e621dbcfd57d8a16e8cd5d38a836a31)
        ;

        
    
    
            var marker_78fc25ae15b58784aebcb0c44ad16423 = L.marker(
                [24.82509935897436, 46.64812147435897],
                {
}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
            var div_icon_edfe374131bc3d8f39b711d5725ddcde = L.divIcon({
  "html": "\u003cdiv style=\"font-size:10pt; color:white; font-weight:bold;\"\u003e23\u003c/div\u003e",
  "iconSize": [20, 20],
  "iconAnchor": [10, 10],
  "className": "empty",
});
        
    
                marker_78fc25ae15b58784aebcb0c44ad16423.setIcon(div_icon_edfe374131bc3d8f39b711d5725ddcde);
            
    
            var circle_marker_28c4f1dbb81e647c14b8ae25fd082c3a = L.circleMarker(
                [24.76172127071823, 46.774360220994474],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.6, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 17.79354285266583, "stroke": true, "weight": 3}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
        var popup_b49ba185b95726c76aab68b27e480106 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_b10f02c38a295623fd446afabe9acdf8 = $(`<div id="html_b10f02c38a295623fd446afabe9acdf8" style="width: 100.0%; height: 100.0%;">             <b>Rank: 17</b><br>             <b>Branch:</b> PHARMACIATY57<br>             <b>Order Volume:</b> 362<br>             <b>Tier:</b> Tier 2<br>             <b>Distance:</b> 8.6 km             </div>`)[0];
                popup_b49ba185b95726c76aab68b27e480106.setContent(html_b10f02c38a295623fd446afabe9acdf8);
            
        

        circle_marker_28c4f1dbb81e647c14b8ae25fd082c3a.bindPopup(popup_b49ba185b95726c76aab68b27e480106)
        ;

        
    
    
            var marker_0fa54aea039d81aa98268348029be436 = L.marker(
                [24.76172127071823, 46.774360220994474],
                {
}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
            var div_icon_42c5ed5115d3e4451011941f672f6355 = L.divIcon({
  "html": "\u003cdiv style=\"font-size:10pt; color:white; font-weight:bold;\"\u003e17\u003c/div\u003e",
  "iconSize": [20, 20],
  "iconAnchor": [10, 10],
  "className": "empty",
});
        
    
                marker_0fa54aea039d81aa98268348029be436.setIcon(div_icon_42c5ed5115d3e4451011941f672f6355);
            
    
            var circle_marker_b4f3f3351745e06ca72b24af00896fae = L.circleMarker(
                [24.75913183183183, 46.8172048048048],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.6, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 17.6122211675316, "stroke": true, "weight": 3}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
        var popup_508ae149a4792accc7d8ab17972921dc = L.popup({
  "maxWidth": 200,
});

        
            
                var html_ee71aee8130abdc6788a3b1bf35ab780 = $(`<div id="html_ee71aee8130abdc6788a3b1bf35ab780" style="width: 100.0%; height: 100.0%;">             <b>Rank: 21</b><br>             <b>Branch:</b> PHARMACIATY59<br>             <b>Order Volume:</b> 333<br>             <b>Tier:</b> Tier 2<br>             <b>Distance:</b> 8.1 km             </div>`)[0];
                popup_508ae149a4792accc7d8ab17972921dc.setContent(html_ee71aee8130abdc6788a3b1bf35ab780);
            
        

        circle_marker_b4f3f3351745e06ca72b24af00896fae.bindPopup(popup_508ae149a4792accc7d8ab17972921dc)
        ;

        
    
    
            var marker_b0474c06ba36cd115cd50e2adbf90833 = L.marker(
                [24.75913183183183, 46.8172048048048],
                {
}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
            var div_icon_3edf632d38907b5c63b067ad04f008ca = L.divIcon({
  "html": "\u003cdiv style=\"font-size:10pt; color:white; font-weight:bold;\"\u003e21\u003c/div\u003e",
  "iconSize": [20, 20],
  "iconAnchor": [10, 10],
  "className": "empty",
});
        
    
                marker_b0474c06ba36cd115cd50e2adbf90833.setIcon(div_icon_3edf632d38907b5c63b067ad04f008ca);
            
    
            var circle_marker_6c58a4d61efe3edf71d738810824a282 = L.circleMarker(
                [24.587217786561265, 46.60894031620553],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.6, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 18.520752584198995, "stroke": true, "weight": 3}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
        var popup_a7c5c9dc9bee212513eac4ac352ca8aa = L.popup({
  "maxWidth": 200,
});

        
            
                var html_c6117dcf8de00c52df8ffb31199bd45e = $(`<div id="html_c6117dcf8de00c52df8ffb31199bd45e" style="width: 100.0%; height: 100.0%;">             <b>Rank: 4</b><br>             <b>Branch:</b> PHARMACIATY6<br>             <b>Order Volume:</b> 506<br>             <b>Tier:</b> Tier 3<br>             <b>Distance:</b> 22.3 km             </div>`)[0];
                popup_a7c5c9dc9bee212513eac4ac352ca8aa.setContent(html_c6117dcf8de00c52df8ffb31199bd45e);
            
        

        circle_marker_6c58a4d61efe3edf71d738810824a282.bindPopup(popup_a7c5c9dc9bee212513eac4ac352ca8aa)
        ;

        
    
    
            var marker_c3e87deb61862e625fe6b2d8d3ffdf25 = L.marker(
                [24.587217786561265, 46.60894031620553],
                {
}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
            var div_icon_a463204d48c84116248a335172c24f2c = L.divIcon({
  "html": "\u003cdiv style=\"font-size:10pt; color:white; font-weight:bold;\"\u003e4\u003c/div\u003e",
  "iconSize": [20, 20],
  "iconAnchor": [10, 10],
  "className": "empty",
});
        
    
                marker_c3e87deb61862e625fe6b2d8d3ffdf25.setIcon(div_icon_a463204d48c84116248a335172c24f2c);
            
    
            var circle_marker_b7639e95117b609e7f0add2347479c6e = L.circleMarker(
                [24.811776178010472, 46.89323821989529],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.6, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 17.910316814558545, "stroke": true, "weight": 3}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
        var popup_aa829e9838b93c1d60953eb9aa35c855 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_3d13b067181218d3c9b7ba2186c6d060 = $(`<div id="html_3d13b067181218d3c9b7ba2186c6d060" style="width: 100.0%; height: 100.0%;">             <b>Rank: 15</b><br>             <b>Branch:</b> PHARMACIATY80<br>             <b>Order Volume:</b> 382<br>             <b>Tier:</b> Tier 3<br>             <b>Distance:</b> 16.7 km             </div>`)[0];
                popup_aa829e9838b93c1d60953eb9aa35c855.setContent(html_3d13b067181218d3c9b7ba2186c6d060);
            
        

        circle_marker_b7639e95117b609e7f0add2347479c6e.bindPopup(popup_aa829e9838b93c1d60953eb9aa35c855)
        ;

        
    
    
            var marker_caad26be85c3551928b287a9cccc27cb = L.marker(
                [24.811776178010472, 46.89323821989529],
                {
}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
            var div_icon_d5c3dc30fdb0726b04ca2438d89f07ee = L.divIcon({
  "html": "\u003cdiv style=\"font-size:10pt; color:white; font-weight:bold;\"\u003e15\u003c/div\u003e",
  "iconSize": [20, 20],
  "iconAnchor": [10, 10],
  "className": "empty",
});
        
    
                marker_caad26be85c3551928b287a9cccc27cb.setIcon(div_icon_d5c3dc30fdb0726b04ca2438d89f07ee);
            
    
            var circle_marker_e4a97859f0a7e61fcad997cfb4e8bc32 = L.circleMarker(
                [24.800831381733023, 46.88558454332553],
                {"bubblingMouseEvents": true, "color": "#d62728", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d62728", "fillOpacity": 0.6, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 18.15213937512512, "stroke": true, "weight": 3}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
        var popup_459fc583822490dbdb2a357aa1eac805 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_1a69788f2e67f4f51bbb67450fd63cdc = $(`<div id="html_1a69788f2e67f4f51bbb67450fd63cdc" style="width: 100.0%; height: 100.0%;">             <b>Rank: 10</b><br>             <b>Branch:</b> PHARMACIATY81<br>             <b>Order Volume:</b> 427<br>             <b>Tier:</b> Tier 3<br>             <b>Distance:</b> 15.2 km             </div>`)[0];
                popup_459fc583822490dbdb2a357aa1eac805.setContent(html_1a69788f2e67f4f51bbb67450fd63cdc);
            
        

        circle_marker_e4a97859f0a7e61fcad997cfb4e8bc32.bindPopup(popup_459fc583822490dbdb2a357aa1eac805)
        ;

        
    
    
            var marker_8302e24218a6f65e17026734092d6030 = L.marker(
                [24.800831381733023, 46.88558454332553],
                {
}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
            var div_icon_ba300239ebaa857b3436e5fb33c111ad = L.divIcon({
  "html": "\u003cdiv style=\"font-size:10pt; color:white; font-weight:bold;\"\u003e10\u003c/div\u003e",
  "iconSize": [20, 20],
  "iconAnchor": [10, 10],
  "className": "empty",
});
        
    
                marker_8302e24218a6f65e17026734092d6030.setIcon(div_icon_ba300239ebaa857b3436e5fb33c111ad);
            
    
            var circle_marker_a64994d2f54b6b16945f807fd02aec64 = L.circleMarker(
                [24.608267465753425, 46.727524657534246],
                {"bubblingMouseEvents": true, "color": "#1f77b4", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#1f77b4", "fillOpacity": 0.6, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 17.32691425724209, "stroke": true, "weight": 3}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
        var popup_22577f93b963f9ada45eb827c793d889 = L.popup({
  "maxWidth": 200,
});

        
            
                var html_68410af327883df053c935f5ea83ce89 = $(`<div id="html_68410af327883df053c935f5ea83ce89" style="width: 100.0%; height: 100.0%;">             <b>Rank: 26</b><br>             <b>Branch:</b> PHARMACIATY98<br>             <b>Order Volume:</b> 292<br>             <b>Tier:</b> Tier 2<br>             <b>Distance:</b> 11.5 km             </div>`)[0];
                popup_22577f93b963f9ada45eb827c793d889.setContent(html_68410af327883df053c935f5ea83ce89);
            
        

        circle_marker_a64994d2f54b6b16945f807fd02aec64.bindPopup(popup_22577f93b963f9ada45eb827c793d889)
        ;

        
    
    
            var marker_7b1220af58609276ec46a7a245a5a6a0 = L.marker(
                [24.608267465753425, 46.727524657534246],
                {
}
            ).addTo(map_ea04b933ce08162cc5c7486923a91d62);
        
    
            var div_icon_66d39c6b4fa5de13ca05de10c018c0d9 = L.divIcon({
  "html": "\u003cdiv style=\"font-size:10pt; color:white; font-weight:bold;\"\u003e26\u003c/div\u003e",
  "iconSize": [20, 20],
  "iconAnchor": [10, 10],
  "className": "empty",
});
        
    
                marker_7b1220af58609276ec46a7a245a5a6a0.setIcon(div_icon_66d39c6b4fa5de13ca05de10c018c0d9);
            
</script>
</html>