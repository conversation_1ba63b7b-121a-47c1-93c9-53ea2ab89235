"""
Utility functions for Wasfaty Analytics
"""

import pandas as pd
import numpy as np
import re
from datetime import datetime

def clean_merged_logs(file_path):
    """
    Clean the merged logs CSV file by removing headers and file separators
    
    Args:
        file_path (str): Path to the merged logs CSV file
        
    Returns:
        pd.DataFrame: Cleaned dataframe
    """
    print("🔄 Loading and cleaning data...")
    
    # Read the raw file
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Filter out file separators and duplicate headers
    cleaned_lines = []
    header_added = False
    
    for line in lines:
        line = line.strip()
        
        # Skip file separator lines
        if line.startswith('==> logs_'):
            continue
            
        # Skip empty lines
        if not line:
            continue
            
        # Add header only once
        if line.startswith('timestamp,'):
            if not header_added:
                cleaned_lines.append(line)
                header_added = True
            continue
            
        # Add data lines
        if ',' in line and not line.startswith('timestamp,'):
            cleaned_lines.append(line)
    
    # Create DataFrame from cleaned lines
    if cleaned_lines:
        # Write to temporary string and read as CSV
        csv_content = '\n'.join(cleaned_lines)
        from io import StringIO
        df = pd.read_csv(StringIO(csv_content))
        
        # Data type conversions - ensure timezone-naive datetimes
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        if hasattr(df['timestamp'].dtype, 'tz') and df['timestamp'].dtype.tz is not None:
            df['timestamp'] = df['timestamp'].dt.tz_localize(None)

        df['delivery_date'] = pd.to_datetime(df['delivery_date'])
        if hasattr(df['delivery_date'].dtype, 'tz') and df['delivery_date'].dtype.tz is not None:
            df['delivery_date'] = df['delivery_date'].dt.tz_localize(None)
        df['latitude'] = pd.to_numeric(df['latitude'], errors='coerce')
        df['longitude'] = pd.to_numeric(df['longitude'], errors='coerce')
        df['is_pickup'] = df['is_pickup'].astype(bool)
        
        # Add derived columns
        df['hour'] = df['timestamp'].dt.hour
        df['day_of_week'] = df['timestamp'].dt.day_name()
        df['date'] = df['timestamp'].dt.date
        df['week'] = df['timestamp'].dt.isocalendar().week
        df['month'] = df['timestamp'].dt.month

        # Add delivery-specific columns for geospatial analysis
        df['delivery_day_of_week'] = df['delivery_date'].dt.day_name()

        # Extract delivery hour from time slot start time
        try:
            df['delivery_hour_start'] = pd.to_datetime(df['delivery_time_slot_start_time'], format='%H:%M:%S', errors='coerce').dt.hour
        except:
            # If time format is different, try alternative parsing
            df['delivery_hour_start'] = df['delivery_time_slot_start_time'].str.extract(r'(\d+)').astype(float)
        
        # Clean area names
        df['area_clean'] = df['area'].fillna('Unknown')
        
        print(f"✅ Data cleaned successfully! {len(df):,} orders loaded.")
        return df
    else:
        print("❌ No data found in file")
        return pd.DataFrame()

def add_h3_indices(df, resolution=8):
    """
    Add H3 hexagonal indices to the dataframe
    
    Args:
        df (pd.DataFrame): DataFrame with latitude and longitude columns
        resolution (int): H3 resolution level (default: 8)
        
    Returns:
        pd.DataFrame: DataFrame with H3 indices added
    """
    try:
        import h3
        print(f"🔶 Adding H3 indices (resolution {resolution})...")
        
        # Remove rows with missing coordinates
        valid_coords = df.dropna(subset=['latitude', 'longitude']).copy()
        
        # Add H3 indices
        h3_indices = []
        for _, row in valid_coords.iterrows():
            try:
                h3_index = h3.latlng_to_cell(row['latitude'], row['longitude'], resolution)
                h3_indices.append(h3_index)
            except:
                h3_indices.append(None)
        
        valid_coords['h3_index'] = h3_indices
        
        # Merge back with original dataframe
        df_with_h3 = df.merge(
            valid_coords[['order_id', 'h3_index']], 
            on='order_id', 
            how='left'
        )
        
        print(f"✅ H3 indices added! {valid_coords['h3_index'].notna().sum():,} valid indices.")
        return df_with_h3
        
    except ImportError:
        print("⚠️ H3 library not available. Skipping H3 indexing.")
        return df

def get_time_slot_stats(df):
    """
    Analyze delivery time slot preferences
    
    Args:
        df (pd.DataFrame): Orders dataframe
        
    Returns:
        pd.DataFrame: Time slot statistics
    """
    # Create time slot labels
    df = df.copy()
    df['time_slot'] = df['delivery_time_slot_start_time'].astype(str) + ' - ' + df['delivery_time_slot_end_time'].astype(str)
    
    time_stats = df.groupby(['time_slot', 'day_of_week']).size().reset_index(name='order_count')
    
    return time_stats

def get_area_stats(df):
    """
    Calculate order statistics by area
    
    Args:
        df (pd.DataFrame): Orders dataframe
        
    Returns:
        pd.DataFrame: Area statistics
    """
    area_stats = df.groupby('area_clean').agg({
        'order_id': 'count',
        'latitude': 'mean',
        'longitude': 'mean',
        'branch_license': 'nunique'
    }).rename(columns={
        'order_id': 'order_count',
        'branch_license': 'branch_count'
    })
    
    area_stats['order_percentage'] = (area_stats['order_count'] / len(df)) * 100
    
    return area_stats.sort_values('order_count', ascending=False)

def get_branch_stats(df):
    """
    Analyze branch performance
    
    Args:
        df (pd.DataFrame): Orders dataframe
        
    Returns:
        pd.DataFrame: Branch statistics
    """
    branch_stats = df.groupby('branch_license').agg({
        'order_id': 'count',
        'area_clean': 'nunique',
        'latitude': ['mean', 'std'],
        'longitude': ['mean', 'std']
    })
    
    # Flatten column names
    branch_stats.columns = ['_'.join(col).strip() for col in branch_stats.columns]
    branch_stats = branch_stats.rename(columns={
        'order_id_count': 'total_orders',
        'area_clean_nunique': 'areas_served',
        'latitude_mean': 'avg_latitude',
        'latitude_std': 'lat_spread',
        'longitude_mean': 'avg_longitude',
        'longitude_std': 'lng_spread'
    })
    
    # Fill NaN values for branches with only one location
    branch_stats['lat_spread'] = branch_stats['lat_spread'].fillna(0)
    branch_stats['lng_spread'] = branch_stats['lng_spread'].fillna(0)
    
    return branch_stats.sort_values('total_orders', ascending=False)
