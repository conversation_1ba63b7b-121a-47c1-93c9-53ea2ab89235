# 🏥 Wasfaty Analytics - Pharmacy Delivery Intelligence

[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

A comprehensive analytics platform for Wasfaty pharmacy delivery operations, providing geospatial analysis, temporal patterns, and operational insights using advanced data science techniques.

## 🚀 Features

- **Geospatial Analysis**: H3 hexagonal indexing for demand hotspot identification
- **Temporal Patterns**: Time-series analysis of delivery demand
- **Operational Research**: Clustering and optimization models for resource allocation
- **Interactive Visualizations**: Dynamic maps and charts for stakeholder presentations
- **Executive Reporting**: Automated summary generation and KPI tracking

## 📁 Project Structure

```
wasfaty-analytics/
├── 📊 notebooks/           # Jupyter notebooks organized by analysis type
│   ├── geospatial/        # Spatial analysis notebooks
│   ├── temporal/          # Time-series analysis notebooks
│   ├── executive/         # Executive summary and presentations
│   └── exploratory/       # Data exploration and ad-hoc analysis
├── 🐍 src/                # Python source code
│   ├── data_utils.py      # Data cleaning and preprocessing
│   ├── geospatial_utils.py # Geospatial analysis utilities
│   └── __init__.py        # Package initialization
├── 📁 data/               # Data storage
│   ├── raw/              # Original data files
│   └── processed/        # Cleaned and processed data
├── 📈 outputs/            # Analysis outputs
│   ├── maps/             # Interactive HTML maps
│   ├── kml/              # KML files for Google Earth
│   ├── reports/          # Generated reports
│   └── images/           # Charts and visualizations
├── 📚 docs/               # Documentation
├── 🧪 tests/              # Unit tests
├── ⚙️ config/             # Configuration files
├── 🔧 scripts/            # Utility scripts
├── requirements.txt       # Python dependencies
└── setup.py              # Package setup
```

## 🛠️ Quick Start

### Prerequisites

- Python 3.8 or higher
- pip package manager
- Git (for cloning the repository)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd wasfaty-analytics
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   # Or for development
   pip install -e .[dev]
   ```

4. **Launch Jupyter**
   ```bash
   jupyter lab
   ```

### First Analysis

1. Place your data files in `data/raw/`
2. Open `notebooks/exploratory/wasfaty_analytics.ipynb`
3. Follow the step-by-step analysis workflow

## 📊 Analysis Workflows

### Geospatial Analysis
- **Location**: `notebooks/geospatial/`
- **Purpose**: Identify delivery demand hotspots and optimize coverage areas
- **Key Outputs**: Interactive maps, H3 hexagon analysis, coverage optimization

### Temporal Analysis
- **Location**: `notebooks/temporal/`
- **Purpose**: Understand demand patterns over time
- **Key Outputs**: Time-series charts, seasonal patterns, peak hour analysis

### Executive Reporting
- **Location**: `notebooks/executive/`
- **Purpose**: Generate stakeholder-ready summaries and presentations
- **Key Outputs**: Executive dashboards, KPI reports, strategic recommendations

## 🔧 Configuration

Configuration files are stored in the `config/` directory:
- `objective.txt`: Project objectives and goals
- Additional configuration files as needed

## 📖 Documentation

Detailed documentation is available in the `docs/` folder:
- API documentation
- User guides
- Contributing guidelines
- Troubleshooting

## 🧪 Testing

Run tests to ensure code quality:
```bash
pytest tests/
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For questions or support, contact the Wasfaty Analytics Team.

---

**Built with ❤️ for better pharmacy delivery operations**
