# Wasfaty Geospatial Demand Analysis

## Overview
This project provides a comprehensive geospatial and temporal demand analysis for Wasfaty pharmacy delivery operations. It leverages H3 hexagonal indexing and operational research (OR) models to help operations teams understand demand patterns and optimize resource planning.

## Data Description
The analysis uses a `merged_logs.csv` file with the following columns:
- `timestamp`: Order timestamp
- `branch_license`: Branch identifier
- `delivery_date`: Date of delivery
- `delivery_time_slot_start_time`, `delivery_time_slot_end_time`: Delivery time slot
- `erx_reference`, `is_pickup`, `order_id`: Order details
- `first_name`, `last_name`, `gender`, `member_id`, `national_id`: Customer details
- `area`: Area name
- `latitude`, `longitude`: Geographic coordinates

## Methodology
1. **Data Preparation**: Load and clean the data, engineer features (day of week, time of day, etc.).
2. **H3 Geospatial Indexing**: Assign H3 hexagons to each order using latitude/longitude, aggregate demand by H3 cell, and visualize hotspots.
3. **Temporal Analysis**: Analyze demand by day of week and time of day (morning/afternoon/evening/night), and create heatmaps.
4. **Operational Research (OR) Models**: Use clustering or location-allocation heuristics to suggest optimal resource allocation or branch locations.

## Requirements
- Python 3.8+
- pandas, numpy, matplotlib, seaborn, plotly, folium
- h3 (install via `pip install h3`)
- scikit-learn (for clustering/OR models)

Install dependencies:
```bash
pip install -r requirements.txt
```

## Usage
1. Place your `merged_logs.csv` file in the `daily_logs/` directory.
2. Open and run the `geospatial_demand_analysis.ipynb` notebook in Jupyter.
3. Follow the notebook sections for data loading, geospatial analysis, temporal analysis, and operational recommendations.

## Outputs
- Interactive maps of demand hotspots
- Temporal demand heatmaps
- OR-based resource planning suggestions

## Interpretation
- Use the H3 hotspot maps to identify high-demand areas for resource allocation.
- Use temporal patterns to plan staffing and delivery schedules.
- Use OR model outputs for strategic decisions on branch placement or resource deployment.

---
For questions or improvements, please contact the analytics team. 